"""
数据处理模块
包含所有数据清洗、转换和处理的业务逻辑
"""
import pandas as pd
import re
from datetime import datetime


def filter_products(df, product_col='物料名称'):
    """Apply comprehensive data filtering logic from original Python script
    FOR INVENTORY DATA ONLY - excludes fresh products
    """
    print(f"开始筛选库存数据，原始记录数: {len(df)}")

    # 1. Filter out fresh products (starting with '鲜') except '凤肠' products
    mask = ~df[product_col].str.startswith('鲜', na=False) | df[product_col].str.contains('凤肠', na=False)
    df = df[mask]
    print(f"排除鲜品（保留凤肠）后，剩余 {len(df)} 条记录")

    # 2. Filter by customer name - exclude empty, '副产品', '鲜品'
    if '客户名称' in df.columns:
        original_count = len(df)
        df['客户名称'] = df['客户名称'].fillna('').astype(str).str.strip()
        excluded_customers = ['', '副产品', '鲜品']
        df = df[~df['客户名称'].str.lower().isin([x.lower() for x in excluded_customers])]
        print(f"排除客户名称为空白、副产品、鲜品后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    # 3. Filter by material category - exclude empty, '副产品', '生鲜品其他'
    if '物料分类' in df.columns:
        original_count = len(df)
        df['物料分类'] = df['物料分类'].fillna('').astype(str).str.strip()
        excluded_categories = ['', '副产品', '生鲜品其他']
        df = df[~df['物料分类'].str.lower().isin([x.lower() for x in excluded_categories])]
        print(f"排除物料分类为空白、副产品、生鲜品其他后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    # 4. Filter by material category name - exclude empty, '副产品', '生鲜品其他'
    if '物料分类名称' in df.columns:
        original_count = len(df)
        df['物料分类名称'] = df['物料分类名称'].fillna('').astype(str).str.strip()
        excluded_categories = ['', '副产品', '生鲜品其他']
        df = df[~df['物料分类名称'].str.lower().isin([x.lower() for x in excluded_categories])]
        print(f"排除物料分类名称为空白、副产品、生鲜品其他后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    # 5. Filter by material major category - exclude empty, '副产品'
    if '物料大类' in df.columns:
        original_count = len(df)
        df['物料大类'] = df['物料大类'].fillna('').astype(str).str.strip()
        excluded_major_categories = ['', '副产品']
        df = df[~df['物料大类'].str.lower().isin([x.lower() for x in excluded_major_categories])]
        print(f"排除物料大类为空白、副产品后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    print(f"库存数据筛选完成，最终剩余 {len(df)} 条记录")
    return df


def filter_sales_products(df, product_col='物料名称'):
    """Apply filtering logic specifically for SALES DATA
    INCLUDES fresh products but excludes by-products and blanks
    """
    print(f"开始筛选销售数据，原始记录数: {len(df)}")

    # 1. Filter by material category - exclude empty and '副产品' only
    if '物料分类' in df.columns:
        original_count = len(df)
        df['物料分类'] = df['物料分类'].fillna('').astype(str).str.strip()
        excluded_categories = ['', '副产品']
        df = df[~df['物料分类'].str.lower().isin([x.lower() for x in excluded_categories])]
        print(f"排除物料分类为空白、副产品后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    # 2. Filter by customer name - exclude empty and '副产品' only (keep fresh products)
    if '客户名称' in df.columns:
        original_count = len(df)
        df['客户名称'] = df['客户名称'].fillna('').astype(str).str.strip()
        excluded_customers = ['', '副产品']
        df = df[~df['客户名称'].str.lower().isin([x.lower() for x in excluded_customers])]
        print(f"排除客户名称为空白、副产品后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    # 3. Filter by material category name - exclude empty and '副产品' only
    if '物料分类名称' in df.columns:
        original_count = len(df)
        df['物料分类名称'] = df['物料分类名称'].fillna('').astype(str).str.strip()
        excluded_categories = ['', '副产品']
        df = df[~df['物料分类名称'].str.lower().isin([x.lower() for x in excluded_categories])]
        print(f"排除物料分类名称为空白、副产品后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    # 4. Filter by material major category - exclude empty and '副产品' only
    if '物料大类' in df.columns:
        original_count = len(df)
        df['物料大类'] = df['物料大类'].fillna('').astype(str).str.strip()
        excluded_major_categories = ['', '副产品']
        df = df[~df['物料大类'].str.lower().isin([x.lower() for x in excluded_major_categories])]
        print(f"排除物料大类为空白、副产品后，从 {original_count} 条记录中剩余 {len(df)} 条记录")

    print(f"销售数据筛选完成，最终剩余 {len(df)} 条记录")
    return df


def filter_products_for_inventory(df, product_col='物料名称'):
    """按照正确的7步数据清洗逻辑处理库存数据"""
    print(f"开始筛选库存数据，原始记录数: {len(df)}")
    original_inventory = pd.to_numeric(df['结存'], errors='coerce').sum()
    print(f"原始库存总量: {original_inventory:.2f} kg = {original_inventory/1000:.2f} 吨")

    # 步骤2: 移除无效行
    inventory_df = df.dropna(how='all')
    inventory_df = inventory_df[inventory_df['物料名称'].notna() & (inventory_df['物料名称'] != '')]
    print(f"步骤2完成: 移除无效行后，剩余 {len(inventory_df)} 条记录")

    # 步骤3: 特殊保留"凤肠"产品
    feng_chang_products = inventory_df[inventory_df['物料名称'].astype(str).str.contains('凤肠', case=False, na=False)].copy()
    print(f"步骤3: 识别到 {len(feng_chang_products)} 条凤肠产品数据")

    # 步骤4: 按"客户"列筛选
    if '客户' in inventory_df.columns:
        before_count = len(inventory_df)
        inventory_df = inventory_df[
            inventory_df['客户'].notna() & 
            (inventory_df['客户'].astype(str).str.strip() != '') &
            (~inventory_df['客户'].astype(str).str.strip().isin(['副产品', '鲜品']))
        ]
        print(f"步骤4完成: 客户筛选后，从 {before_count} 条记录中剩余 {len(inventory_df)} 条记录")
    else:
        print("步骤4: 未找到'客户'列，跳过客户筛选")

    # 步骤5: 按"物料分类名称"列筛选
    if '物料分类名称' in inventory_df.columns:
        before_count = len(inventory_df)
        inventory_df = inventory_df[
            inventory_df['物料分类名称'].notna() & 
            (inventory_df['物料分类名称'].astype(str).str.strip() != '') &
            (~inventory_df['物料分类名称'].astype(str).str.strip().isin(['副产品', '生鲜品其他']))
        ]
        print(f"步骤5完成: 物料分类名称筛选后，从 {before_count} 条记录中剩余 {len(inventory_df)} 条记录")
    else:
        print("步骤5: 未找到'物料分类名称'列，跳过物料分类筛选")

    # 步骤6: 从物料名称中筛选"鲜"产品
    before_count = len(inventory_df)
    inventory_df = inventory_df[~inventory_df['物料名称'].astype(str).str.contains('鲜', case=False, na=False)]
    print(f"步骤6完成: 筛选鲜产品后，从 {before_count} 条记录中剩余 {len(inventory_df)} 条记录")

    # 步骤7: 重新添加"凤肠"产品
    if len(feng_chang_products) > 0:
        inventory_df = pd.concat([inventory_df, feng_chang_products], ignore_index=True)
        inventory_df = inventory_df.drop_duplicates()
        print(f"步骤7完成: 重新添加 {len(feng_chang_products)} 条凤肠产品数据")

    final_inventory = pd.to_numeric(inventory_df['结存'], errors='coerce').sum()
    print(f"库存筛选完成，最终剩余 {len(inventory_df)} 条记录")
    print(f"最终库存总量: {final_inventory:.2f} kg = {final_inventory/1000:.2f} 吨")
    
    return inventory_df


def extract_date_info(sheet_name):
    """Extract date information from sheet name like '价格表4月2号(2)', '7月12日', or '7.12'"""
    try:
        # Try simple numeric format with optional change count: "7.12", "7.22 (2)", "7.19 (3)" etc.
        simple_pattern = r'^(\d+)\.(\d+)(?:\s*\((\d+)\))?$'
        simple_match = re.search(simple_pattern, sheet_name.strip())
        
        if simple_match:
            month = int(simple_match.group(1))
            day = int(simple_match.group(2))
            change_count = int(simple_match.group(3)) if simple_match.group(3) else 1
            
            # Validate month and day
            if 1 <= month <= 12 and 1 <= day <= 31:
                return (month, day, change_count)
            else:
                print(f"Warning: Invalid date in sheet name '{sheet_name}': month={month}, day={day}")
                return None
        
        # Try Chinese format with optional change count: "7月12日", "4月2号", "7月22日 (2)"
        chinese_pattern = r'^(\d+)月(\d+)[日号](?:\s*\((\d+)\))?$'
        chinese_match = re.search(chinese_pattern, sheet_name.strip())
        
        if chinese_match:
            month = int(chinese_match.group(1))
            day = int(chinese_match.group(2))
            change_count = int(chinese_match.group(3)) if chinese_match.group(3) else 1
            
            # Validate month and day
            if 1 <= month <= 12 and 1 <= day <= 31:
                return (month, day, change_count)
            else:
                print(f"Warning: Invalid date in sheet name '{sheet_name}': month={month}, day={day}")
                return None
        
        # Try old format with "价格表": 价格表4月2号(2), 价格表4月2号（2）, 价格表4月2号
        old_pattern = r'价格表(\d+)月(\d+)号(?:[（(](\d+)[）)])?'
        old_match = re.search(old_pattern, sheet_name)
        
        if old_match:
            month = int(old_match.group(1))
            day = int(old_match.group(2))
            change_count = int(old_match.group(3)) if old_match.group(3) else 1
            
            # Validate month and day
            if 1 <= month <= 12 and 1 <= day <= 31:
                return (month, day, change_count)
            else:
                print(f"Warning: Invalid date in sheet name '{sheet_name}': month={month}, day={day}")
                return None
        else:
            print(f"Warning: Sheet name '{sheet_name}' does not match any expected format")
            return None
            
    except Exception as e:
        print(f"Error parsing sheet name '{sheet_name}': {e}")
        return None


def is_new_format_date(month, day):
    """Determine if a date uses the new format structure (from 7.12 onwards)"""
    # 7.12 is July 12th
    if month > 7:
        return True
    elif month == 7 and day >= 12:
        return True
    else:
        return False


def process_price_adjustments_data(xls):
    """Process price adjustment data from 调价表.xlsx with support for both old and new column structures"""
    if xls is None:
        print("No price adjustment data to process")
        return pd.DataFrame()
    
    print("Processing price adjustment data...")
    all_processed_data = []
    
    for sheet_name in xls.sheet_names:
        print(f"Processing sheet: {sheet_name}")
        
        # Extract date info from sheet name
        date_info = extract_date_info(sheet_name)
        if date_info is None:
            continue
            
        month, day, change_count = date_info
        # Assuming current year (2025 based on design doc)
        adjustment_date = f"2025-{month:02d}-{day:02d}"
        
        # Determine format based on date
        use_new_structure = is_new_format_date(month, day)
        
        print(f"Using {'new' if use_new_structure else 'old'} column structure for sheet '{sheet_name}' (date: {adjustment_date})")
        
        try:
            # Read the sheet
            df_sheet = pd.read_excel(xls, sheet_name=sheet_name, header=None)
            
            if df_sheet.empty:
                print(f"Sheet '{sheet_name}' is empty, skipping")
                continue
            
            sheet_data = []
            
            if use_new_structure:
                # New structure (7.12 onwards): templates at columns 0-9, 10-19, 20-29
                # CRITICAL FIX: 新格式中品名在第2列，不是第1列！
                templates = [
                    {
                        'name': 'Template 1 (0-9)',
                        'start': 0,
                        'end': 10,
                        'category_col': 0,        # 分类
                        'product_code_col': 1,    # 编码 (新增)
                        'product_name_col': 2,    # 品名 (修复：从1改为2)
                        'specification_col': 3,   # 规格 (修复：从2改为3)
                        'previous_price_col': 8,  # 前价格 (加工二厂) (修复：从7改为8)
                        'current_price_col': 9    # 价格 (加工二厂) (修复：从8改为9)
                    },
                    {
                        'name': 'Template 2 (10-19)', 
                        'start': 10,
                        'end': 20,
                        'category_col': 10,       # 分类
                        'product_code_col': 11,   # 编码 (新增)
                        'product_name_col': 12,   # 品名 (修复：从11改为12)
                        'specification_col': 13,  # 规格 (修复：从12改为13)
                        'previous_price_col': 18, # 前价格 (加工二厂) (修复：从17改为18)
                        'current_price_col': 19   # 价格 (加工二厂) (修复：从18改为19)
                    },
                    {
                        'name': 'Template 3 (20-29)',
                        'start': 20,
                        'end': 30,
                        'category_col': 20,       # 分类
                        'product_code_col': 21,   # 编码 (新增)
                        'product_name_col': 22,   # 品名 (修复：从21改为22)
                        'specification_col': 23,  # 规格 (修复：从22改为23)
                        'previous_price_col': 28, # 前价格 (加工二厂) (修复：从27改为28)
                        'current_price_col': 29   # 价格 (加工二厂) (修复：从28改为29)
                    }
                ]
                
                for template in templates:
                    print(f"Processing {template['name']}")
                    
                    # Check if template columns exist
                    if df_sheet.shape[1] <= template['start']:
                        print(f"Not enough columns for {template['name']}")
                        continue
                    
                    # Extract template data (skip header row)
                    for idx in range(1, len(df_sheet)):
                        row = df_sheet.iloc[idx]
                        
                        # Skip if not enough columns for this template
                        max_col = max(template['product_name_col'], template['specification_col'], 
                                    template['previous_price_col'], template['current_price_col'])
                        if len(row) <= max_col:
                            continue
                        
                        # Extract columns for this template
                        category = row.iloc[template['category_col']] if template['category_col'] < len(row) else None
                        product_code = row.iloc[template['product_code_col']] if template['product_code_col'] < len(row) else None
                        product_name = row.iloc[template['product_name_col']] if template['product_name_col'] < len(row) else None
                        specification = row.iloc[template['specification_col']] if template['specification_col'] < len(row) else None
                        previous_price = row.iloc[template['previous_price_col']] if template['previous_price_col'] < len(row) else None
                        current_price = row.iloc[template['current_price_col']] if template['current_price_col'] < len(row) else None
                        
                        # Clean and validate data
                        if (product_name is None or 
                            pd.isna(product_name) or 
                            str(product_name).strip() == '' or
                            '均价' in str(product_name) or
                            '品名' in str(product_name)):
                            continue
                        
                        # Convert prices to numeric
                        try:
                            previous_price = pd.to_numeric(previous_price, errors='coerce')
                            current_price = pd.to_numeric(current_price, errors='coerce')
                        except:
                            previous_price = None
                            current_price = None
                        
                        # Skip if no valid current price
                        if pd.isna(current_price) or current_price is None:
                            continue
                        
                        # Calculate price change
                        price_change = (current_price - previous_price) if (previous_price is not None and not pd.isna(previous_price)) else 0
                        
                        # Create record
                        record = {
                            'category': str(category).strip() if category is not None and not pd.isna(category) else None,
                            'product_name': str(product_name).strip(),
                            'specification': str(specification).strip() if specification is not None and not pd.isna(specification) else None,
                            'adjustment_date': adjustment_date,
                            'previous_price': previous_price if not pd.isna(previous_price) else None,
                            'current_price': current_price,
                            'price_change': price_change,
                            'adjustment_count': change_count
                        }
                        
                        sheet_data.append(record)
            
            else:
                # Old structure (before 7.12): templates at columns 0-8, 9-17, 18-26
                templates = [
                    {'name': 'Template 1 (0-8)', 'start': 0, 'end': 9},
                    {'name': 'Template 2 (9-17)', 'start': 9, 'end': 18}, 
                    {'name': 'Template 3 (18-26)', 'start': 18, 'end': 27}
                ]
                
                for template in templates:
                    print(f"Processing {template['name']}")
                    
                    # Check if template columns exist
                    if df_sheet.shape[1] <= template['start']:
                        print(f"Not enough columns for {template['name']}")
                        continue
                        
                    # Extract template data (skip header row)
                    for idx in range(1, len(df_sheet)):
                        row = df_sheet.iloc[idx]
                        
                        # Skip if not enough columns
                        if len(row) <= template['start'] + 8:
                            continue
                        
                        # Extract columns for this template (old structure)
                        category = row.iloc[template['start']] if template['start'] < len(row) else None
                        product_name = row.iloc[template['start'] + 1] if template['start'] + 1 < len(row) else None
                        specification = row.iloc[template['start'] + 2] if template['start'] + 2 < len(row) else None
                        
                        # Extract "加工二厂" data (columns 7 and 8 relative to template start)
                        previous_price = row.iloc[template['start'] + 7] if template['start'] + 7 < len(row) else None
                        current_price = row.iloc[template['start'] + 8] if template['start'] + 8 < len(row) else None
                        
                        # Clean and validate data
                        if (product_name is None or 
                            pd.isna(product_name) or 
                            str(product_name).strip() == '' or
                            '均价' in str(product_name) or
                            '品名' in str(product_name)):
                            continue
                        
                        # Convert prices to numeric
                        try:
                            previous_price = pd.to_numeric(previous_price, errors='coerce')
                            current_price = pd.to_numeric(current_price, errors='coerce')
                        except:
                            previous_price = None
                            current_price = None
                        
                        # Skip if no valid current price
                        if pd.isna(current_price) or current_price is None:
                            continue
                        
                        # Calculate price change
                        price_change = (current_price - previous_price) if (previous_price is not None and not pd.isna(previous_price)) else 0
                        
                        # Create record
                        record = {
                            'category': str(category).strip() if category is not None and not pd.isna(category) else None,
                            'product_name': str(product_name).strip(),
                            'specification': str(specification).strip() if specification is not None and not pd.isna(specification) else None,
                            'adjustment_date': adjustment_date,
                            'previous_price': previous_price if not pd.isna(previous_price) else None,
                            'current_price': current_price,
                            'price_change': price_change,
                            'adjustment_count': change_count
                        }
                        
                        sheet_data.append(record)
            
            if sheet_data:
                print(f"Processed {len(sheet_data)} records from sheet '{sheet_name}' using {'new' if use_new_structure else 'old'} structure")
                all_processed_data.extend(sheet_data)
            else:
                print(f"No valid data found in sheet '{sheet_name}'")
                
        except Exception as e:
            print(f"Error processing sheet '{sheet_name}': {e}")
            continue
    
    # Convert to DataFrame
    if all_processed_data:
        df_price_adjustments = pd.DataFrame(all_processed_data)
        print(f"Total processed price adjustment records: {len(df_price_adjustments)}")
        return df_price_adjustments
    else:
        print("No price adjustment data was processed")
        return pd.DataFrame()


def process_inventory_data(df_inv):
    """Process inventory data and return cleaned DataFrame"""
    print("Processing inventory data...")
    
    if df_inv.empty:
        print("No inventory data to process")
        return pd.DataFrame()
    
    # Apply inventory-specific filtering
    df_inventory_filtered = filter_products_for_inventory(df_inv)
    
    # Extract unique product names from inventory
    unique_products = df_inventory_filtered['物料名称'].unique()
    print(f"Found {len(unique_products)} unique products in inventory data")
    
    return df_inventory_filtered


def process_production_data(df_prod):
    """Process production data and return cleaned DataFrame with date information"""
    print("Processing production data...")
    
    if df_prod.empty:
        print("No production data to process")
        return pd.DataFrame()
    
    # Print available columns for debugging
    print("Available columns:", df_prod.columns.tolist())
    
    # Apply standard filtering
    df_production_filtered = filter_products(df_prod)
    
    # Extract date information if available
    date_columns = ['入库日期', '生产日期', '单据日期', '过账日期']
    date_column = None
    
    for col in date_columns:
        if col in df_production_filtered.columns:
            date_column = col
            break
    
    if date_column:
        print(f"Found date column: {date_column}")
        # Convert to datetime and extract date info
        df_production_filtered['date'] = pd.to_datetime(df_production_filtered[date_column], errors='coerce')
        
        # Get date range
        valid_dates = df_production_filtered['date'].dropna()
        if not valid_dates.empty:
            min_date = valid_dates.min().strftime('%Y-%m-%d')
            max_date = valid_dates.max().strftime('%Y-%m-%d')
            print(f"Production date range: {min_date} to {max_date}")
        
        # Process production data with date aggregation
        required_columns = [date_column, '物料名称', '主数量']
        missing_columns = [col for col in required_columns if col not in df_production_filtered.columns]
        
        if not missing_columns:
            try:
                df_processed = df_production_filtered[required_columns].copy()
                df_processed.columns = ['record_date', 'product_name', 'production_volume']
                
                # Convert data types
                df_processed['record_date'] = pd.to_datetime(df_processed['record_date'], errors='coerce').dt.strftime('%Y-%m-%d')
                df_processed['production_volume'] = pd.to_numeric(df_processed['production_volume'], errors='coerce')
                
                # Remove rows with missing key data
                df_processed = df_processed.dropna(subset=['record_date', 'product_name'])
                
                # Group by date and product to aggregate data
                df_processed = df_processed.groupby(['record_date', 'product_name']).agg({
                    'production_volume': 'sum'
                }).reset_index()
                
                print(f"Processed production data: {len(df_processed)} records")
                if not df_processed.empty:
                    print(f"Date range: {df_processed['record_date'].min()} to {df_processed['record_date'].max()}")
                    print(f"Total production volume: {df_processed['production_volume'].sum():.2f} kg = {df_processed['production_volume'].sum()/1000:.2f} tons")
                
                return df_processed
                
            except Exception as e:
                print(f"Error processing production data with dates: {e}")
                # Fall back to original logic
                pass
    
    # Fallback: return filtered data without date processing
    print("No date column found or processing failed, using original logic")
    unique_products = df_production_filtered['物料名称'].unique()
    print(f"Found {len(unique_products)} unique products in production data")
    
    return df_production_filtered


def process_sales_data(df_sales):
    """Process sales data and return cleaned DataFrame with date information
    Uses relaxed filtering for sales data - includes fresh products
    """
    print("Processing sales data...")
    
    if df_sales.empty:
        print("No sales data to process")
        return pd.DataFrame()
    
    # Print available columns for debugging
    print("Available columns:", df_sales.columns.tolist())
    
    # Apply sales-specific filtering (includes fresh products)
    df_sales_filtered = filter_sales_products(df_sales)
    
    # Extract date information if available
    if '发票日期' in df_sales_filtered.columns:
        # Convert to datetime and extract date info
        df_sales_filtered['date'] = pd.to_datetime(df_sales_filtered['发票日期'], errors='coerce')
        
        # Get date range
        valid_dates = df_sales_filtered['date'].dropna()
        if not valid_dates.empty:
            min_date = valid_dates.min().strftime('%Y-%m-%d')
            max_date = valid_dates.max().strftime('%Y-%m-%d')
            print(f"Date range: {min_date} to {max_date}")
    
    # Extract unique product names from sales
    unique_products = df_sales_filtered['物料名称'].unique()
    print(f"Found {len(unique_products)} unique products in sales data")
    
    # Process sales data if we have the required columns
    required_columns = ['发票日期', '物料名称', '主数量', '本币无税金额', '物料分类']
    missing_columns = [col for col in required_columns if col not in df_sales_filtered.columns]
    
    if missing_columns:
        print(f"Warning: Missing columns: {missing_columns}")
        # Try alternative column names
        if '本币无税金额' not in df_sales_filtered.columns and '无税金额' in df_sales_filtered.columns:
            df_sales_filtered['本币无税金额'] = df_sales_filtered['无税金额']
            print("Using '无税金额' as '本币无税金额'")
    
    # Extract and process sales data
    try:
        df_processed = df_sales_filtered[required_columns].copy()
        df_processed.columns = ['record_date', 'product_name', 'sales_volume', 'tax_free_amount', 'category']
        
        # Convert data types
        df_processed['record_date'] = pd.to_datetime(df_processed['record_date'], errors='coerce').dt.strftime('%Y-%m-%d')
        df_processed['sales_volume'] = pd.to_numeric(df_processed['sales_volume'], errors='coerce')
        df_processed['tax_free_amount'] = pd.to_numeric(df_processed['tax_free_amount'], errors='coerce')
        
        # Remove rows with missing key data (allow negative values for adjustments)
        df_processed = df_processed.dropna(subset=['record_date', 'product_name'])
        
        # Group by date and product to aggregate data (不包括category，避免同一产品多条记录)
        df_processed = df_processed.groupby(['record_date', 'product_name']).agg({
            'sales_volume': 'sum',
            'tax_free_amount': 'sum',
            'category': 'first'  # 取第一个分类作为代表
        }).reset_index()
        
        # Keep sales volume in KG (original unit from Excel)
        # df_processed['sales_volume'] = df_processed['sales_volume'] / 1000  # 移除这个转换
        
        # Calculate average unit price (元/吨)
        # Handle division by zero for negative or zero volumes
        df_processed['average_price'] = 0.0
        mask = df_processed['sales_volume'] != 0
        df_processed.loc[mask, 'average_price'] = (
            df_processed.loc[mask, 'tax_free_amount'] / (df_processed.loc[mask, 'sales_volume'] / 1000)
        ) * 1.09  # 计算单价时需要转换为吨
        
        # Calculate sales amount (tax-inclusive)
        df_processed['sales_amount'] = df_processed['tax_free_amount'] * 1.09
        
        print(f"Processed sales data: {len(df_processed)} records")
        if not df_processed.empty:
            print(f"Date range: {df_processed['record_date'].min()} to {df_processed['record_date'].max()}")
            print(f"Total sales volume: {df_processed['sales_volume'].sum():.2f} kg = {df_processed['sales_volume'].sum()/1000:.2f} tons")
            print(f"Total sales amount: {df_processed['sales_amount'].sum():,.2f} yuan")
        
        return df_processed
        
    except Exception as e:
        print(f"Error processing sales data: {e}")
        return pd.DataFrame()


def get_all_unique_products(*dataframes):
    """Extract all unique product names from multiple DataFrames"""
    all_products = set()
    
    for df in dataframes:
        if not df.empty and '物料名称' in df.columns:
            products = df['物料名称'].dropna().unique()
            all_products.update(products)
        elif not df.empty and 'product_name' in df.columns:
            products = df['product_name'].dropna().unique()
            all_products.update(products)
    
    print(f"Total unique products across all datasets: {len(all_products)}")
    return all_products


if __name__ == "__main__":
    # Test individual functions
    print("Data processors module loaded successfully")
    print("Available functions:")
    print("- filter_products()")
    print("- filter_products_for_inventory()")
    print("- process_inventory_data()")
    print("- process_production_data()")
    print("- process_sales_data()")
    print("- process_price_adjustments_data()")
    print("- extract_date_info()")
    print("- get_all_unique_products()")