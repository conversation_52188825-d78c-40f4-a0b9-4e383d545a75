"""
数据导入主程序
协调各个模块，执行完整的数据导入流程
"""
import sys
import os
from datetime import datetime

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_import.config import (
    print_config_info, validate_config, get_database_mode,
    D1_BATCH_SIZE, get_current_date, get_inventory_business_date, get_production_business_date
)
from data_import.data_loader import load_all_data, validate_data_structure
from data_import.data_processors import (
    process_inventory_data, process_production_data, process_sales_data,
    process_price_adjustments_data, get_all_unique_products
)
from data_import.db_handler import (
    print_database_info, create_products_if_not_exist,
    execute_d1_batch_upsert_optimized, execute_d1_batch_insert,
    verify_products_table, verify_daily_metrics_table, verify_price_adjustments_table
)


def prepare_daily_metrics_data(df_inventory, df_production, df_sales, product_mapping):
    """Prepare data for DailyMetrics table insertion with proper deduplication
    
    使用字典来确保每个产品每天只有一条记录
    """
    print("\n=== Preparing DailyMetrics Data ===")
    
    # 使用字典来存储数据，key为(product_id, date)，确保唯一性
    records_dict = {}
    
    inventory_date = get_inventory_business_date()
    production_date = get_production_business_date()
    
    print(f"使用库存业务日期: {inventory_date}")
    print(f"使用生产业务日期: {production_date}")
    
    # Process inventory data
    if not df_inventory.empty:
        print(f"Processing {len(df_inventory)} inventory records")
        for _, row in df_inventory.iterrows():
            product_name = row['物料名称']
            if product_name in product_mapping:
                product_id = product_mapping[product_name]
                key = (product_id, inventory_date)
                
                if key not in records_dict:
                    records_dict[key] = {
                        'product_id': product_id,
                        'product_name': product_name,
                        'record_date': inventory_date,
                        'inventory_level': 0.0,
                        'production_volume': 0.0,
                        'sales_volume': 0.0,
                        'sales_amount': 0.0
                    }
                
                records_dict[key]['inventory_level'] += float(row.get('结存', 0))
    
    # Process production data
    if not df_production.empty:
        print(f"Processing {len(df_production)} production records")
        
        # Check if this is processed production data (has processed columns)
        if 'product_name' in df_production.columns and 'production_volume' in df_production.columns:
            # This is processed production data with dates - use it directly
            for _, row in df_production.iterrows():
                product_name = row['product_name']
                record_date = row.get('record_date', production_date)
                production_volume = float(row.get('production_volume', 0))
                
                if product_name in product_mapping:
                    product_id = product_mapping[product_name]
                    key = (product_id, record_date)
                    
                    if key not in records_dict:
                        records_dict[key] = {
                            'product_id': product_id,
                            'product_name': product_name,
                            'record_date': record_date,
                            'inventory_level': 0.0,
                            'production_volume': 0.0,
                            'sales_volume': 0.0,
                            'sales_amount': 0.0
                        }
                    
                    records_dict[key]['production_volume'] += production_volume
        else:
            # This is raw production data - use fallback logic
            for _, row in df_production.iterrows():
                product_name = row['物料名称']
                if product_name in product_mapping:
                    product_id = product_mapping[product_name]
                    key = (product_id, production_date)
                    
                    if key not in records_dict:
                        records_dict[key] = {
                            'product_id': product_id,
                            'product_name': product_name,
                            'record_date': production_date,
                            'inventory_level': 0.0,
                            'production_volume': 0.0,
                            'sales_volume': 0.0,
                            'sales_amount': 0.0
                        }
                    
                    records_dict[key]['production_volume'] += float(row.get('主数量', 0))
    
    # Process sales data (already aggregated)
    if not df_sales.empty:
        print(f"Processing {len(df_sales)} processed sales records")
        
        if 'product_name' in df_sales.columns and 'sales_volume' in df_sales.columns:
            for _, row in df_sales.iterrows():
                product_name = row['product_name']
                record_date = row.get('record_date', get_current_date())
                sales_volume = float(row.get('sales_volume', 0))  # 已经是kg
                sales_amount = float(row.get('sales_amount', 0))
                
                if product_name in product_mapping:
                    product_id = product_mapping[product_name]
                    key = (product_id, record_date)
                    
                    if key not in records_dict:
                        records_dict[key] = {
                            'product_id': product_id,
                            'product_name': product_name,
                            'record_date': record_date,
                            'inventory_level': 0.0,
                            'production_volume': 0.0,
                            'sales_volume': 0.0,
                            'sales_amount': 0.0
                        }
                    
                    records_dict[key]['sales_volume'] += sales_volume
                    records_dict[key]['sales_amount'] += sales_amount
    
    # Convert dictionary to list format for database insertion
    batch_data = []
    for record in records_dict.values():
        batch_data.append([
            record['product_id'],
            record['product_name'],
            record['record_date'],
            record['inventory_level'],
            record['production_volume'],
            record['sales_volume'],
            record['sales_amount']
        ])
    
    print(f"Prepared {len(batch_data)} DailyMetrics records (deduplicated)")
    return batch_data


def prepare_price_adjustments_data(df_price_adjustments, product_mapping):
    """Prepare data for PriceAdjustments table insertion"""
    print("\n=== Preparing PriceAdjustments Data ===")
    
    if df_price_adjustments.empty:
        print("No price adjustment data to prepare")
        return []
    
    batch_data = []
    
    for _, row in df_price_adjustments.iterrows():
        product_name = row['product_name']
        if product_name in product_mapping:
            record = [
                product_mapping[product_name],        # product_id
                product_name,                         # product_name
                row.get('specification', ''),         # specification
                row['adjustment_date'],               # adjustment_date
                float(row.get('current_price', 0)),   # current_price
                float(row.get('previous_price', 0)) if row.get('previous_price') is not None else None,  # previous_price
                float(row.get('price_change', 0)),    # price_difference
                int(row.get('adjustment_count', 1))   # adjustment_count
            ]
            batch_data.append(record)
    
    print(f"Prepared {len(batch_data)} PriceAdjustments records")
    return batch_data


def insert_daily_metrics(batch_data):
    """Insert DailyMetrics data into database"""
    if not batch_data:
        print("No DailyMetrics data to insert")
        return True
    
    print(f"\n=== Inserting DailyMetrics Data ===")
    print(f"Inserting {len(batch_data)} records...")
    
    columns = [
        'product_id', 'product_name', 'record_date', 'inventory_level', 
        'production_volume', 'sales_volume', 'sales_amount'
    ]
    
    success = execute_d1_batch_upsert_optimized('DailyMetrics', columns, batch_data)
    
    if success:
        print("✅ DailyMetrics data insertion completed successfully")
    else:
        print("❌ DailyMetrics data insertion failed")
    
    return success


def insert_price_adjustments(batch_data):
    """Insert PriceAdjustments data into database"""
    if not batch_data:
        print("No PriceAdjustments data to insert")
        return True
    
    print(f"\n=== Inserting PriceAdjustments Data ===")
    print(f"Inserting {len(batch_data)} records...")
    
    columns = [
        'product_id', 'product_name', 'specification', 'adjustment_date',
        'current_price', 'previous_price', 'price_difference', 'adjustment_count'
    ]
    
    success = execute_d1_batch_upsert_optimized('PriceAdjustments', columns, batch_data)
    
    if success:
        print("✅ PriceAdjustments data insertion completed successfully")
    else:
        print("❌ PriceAdjustments data insertion failed")
    
    return success


def run_data_verification():
    """Run data verification checks"""
    print("\n" + "="*50)
    print("DATA VERIFICATION")
    print("="*50)
    
    verification_results = []
    
    # Verify Products table
    result = verify_products_table()
    verification_results.append(("Products", result))
    
    # Verify DailyMetrics table
    result = verify_daily_metrics_table()
    verification_results.append(("DailyMetrics", result))
    
    # Verify PriceAdjustments table
    result = verify_price_adjustments_table()
    verification_results.append(("PriceAdjustments", result))
    
    # Summary
    print(f"\n=== Verification Summary ===")
    all_passed = True
    for table_name, passed in verification_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{table_name}: {status}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All verification checks passed!")
    else:
        print("\n⚠️ Some verification checks failed. Please review the logs above.")
    
    return all_passed


def main():
    """Main execution function"""
    print("="*60)
    print("数据导入系统 - 重构版本")
    print("="*60)
    
    # Print configuration
    print_config_info()
    print_database_info()
    
    # Validate configuration
    print("\n=== Configuration Validation ===")
    config_errors = validate_config()
    if config_errors:
        print("❌ Configuration validation failed:")
        for error in config_errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ Configuration validation passed")
    
    try:
        # Step 1: Load data
        print("\n" + "="*50)
        print("STEP 1: LOADING DATA")
        print("="*50)
        
        data = load_all_data()
        
        if not validate_data_structure(data):
            print("❌ Data structure validation failed")
            return False
        
        # Step 2: Process data
        print("\n" + "="*50)
        print("STEP 2: PROCESSING DATA")
        print("="*50)
        
        df_inventory = process_inventory_data(data['inventory'])
        df_production = process_production_data(data['production'])
        df_sales = process_sales_data(data['sales'])
        df_price_adjustments = process_price_adjustments_data(data['price_adjustments'])
        
        # Step 3: Prepare products
        print("\n" + "="*50)
        print("STEP 3: PREPARING PRODUCTS")
        print("="*50)
        
        all_products = get_all_unique_products(
            df_inventory, df_production, df_sales, df_price_adjustments
        )
        
        product_mapping = create_products_if_not_exist(all_products)
        
        if not product_mapping:
            print("❌ Failed to create or retrieve product mappings")
            return False
        
        print(f"✅ Product mapping ready with {len(product_mapping)} products")
        
        # Step 4: Prepare and insert data
        print("\n" + "="*50)
        print("STEP 4: DATABASE INSERTION")
        print("="*50)
        
        # Prepare data for insertion
        daily_metrics_data = prepare_daily_metrics_data(
            df_inventory, df_production, df_sales, product_mapping
        )
        
        price_adjustments_data = prepare_price_adjustments_data(
            df_price_adjustments, product_mapping
        )
        
        # Insert data
        daily_metrics_success = insert_daily_metrics(daily_metrics_data)
        price_adjustments_success = insert_price_adjustments(price_adjustments_data)
        
        if not daily_metrics_success or not price_adjustments_success:
            print("❌ Data insertion failed")
            return False
        
        # Step 5: Verification
        print("\n" + "="*50)
        print("STEP 5: VERIFICATION")
        print("="*50)
        
        verification_success = run_data_verification()
        
        # Final summary
        print("\n" + "="*60)
        print("IMPORT SUMMARY")
        print("="*60)
        
        if verification_success:
            print("🎉 Data import completed successfully!")
            print(f"✅ DailyMetrics: {len(daily_metrics_data)} records")
            print(f"✅ PriceAdjustments: {len(price_adjustments_data)} records")
            print(f"✅ Products: {len(product_mapping)} products")
        else:
            print("⚠️ Data import completed with warnings. Please check verification results.")
        
        return verification_success
        
    except Exception as e:
        print(f"\n❌ Fatal error during data import: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='每日数据导入系统')
    parser.add_argument('--inventory-date', 
                       help='库存数据的业务日期 (YYYY-MM-DD格式)，如不指定则使用config中的默认值')
    
    args = parser.parse_args()
    
    # 如果指定了库存日期，更新配置
    if args.inventory_date:
        import data_import.config as config
        config.INVENTORY_BUSINESS_DATE = args.inventory_date
        print(f"🔧 使用命令行指定的库存业务日期: {args.inventory_date}")
    
    success = main()
    
    if success:
        print(f"\n✅ Script completed successfully at {datetime.now()}")
        sys.exit(0)
    else:
        print(f"\n❌ Script failed at {datetime.now()}")
        sys.exit(1)