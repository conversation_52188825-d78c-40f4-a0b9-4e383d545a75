/**
 * 重构后API功能验证测试脚本
 * 测试所有主要端点是否正常工作
 */

const BASE_URL = 'http://localhost:8787';

// 测试端点列表
const TEST_ENDPOINTS = [
  // 系统端点
  { method: 'GET', path: '/health', description: '健康检查' },
  { method: 'GET', path: '/api/system/health', description: '系统健康检查' },
  { method: 'GET', path: '/api/system/date-range', description: '获取日期范围' },
  { method: 'GET', path: '/api/system/products', description: '获取产品列表' },
  
  // 库存端点
  { method: 'GET', path: '/api/inventory/summary?start_date=2025-06-01&end_date=2025-06-30', description: '库存汇总' },
  { method: 'GET', path: '/api/inventory/total-summary', description: '总库存汇总' },
  { method: 'GET', path: '/api/inventory/top?limit=10', description: 'TOP库存产品' },
  { method: 'GET', path: '/api/inventory/distribution?limit=10', description: '库存分布' },
  { method: 'GET', path: '/api/inventory/trends?start_date=2025-06-01&end_date=2025-06-30', description: '库存趋势' },
  
  // 生产端点
  { method: 'GET', path: '/api/production/ratio-stats?start_date=2025-06-01&end_date=2025-06-30', description: '产销比统计' },
  { method: 'GET', path: '/api/production/validate-consistency?start_date=2025-06-01&end_date=2025-06-30', description: '一致性验证' },
  
  // 销售端点
  { method: 'GET', path: '/api/sales?start_date=2025-06-01&end_date=2025-06-30', description: '销售数据' },
  { method: 'GET', path: '/api/sales/summary?start_date=2025-06-01&end_date=2025-06-30', description: '销售汇总' },
  { method: 'GET', path: '/api/sales/ranking?start_date=2025-06-01&end_date=2025-06-30', description: '销售排名' },
  
  // 价格端点
  { method: 'GET', path: '/api/pricing/changes?start_date=2025-06-01&end_date=2025-06-30', description: '价格变动' },
  { method: 'GET', path: '/api/pricing/trends?start_date=2025-06-01&end_date=2025-06-30', description: '价格趋势' },
  { method: 'GET', path: '/api/pricing/alerts', description: '价格预警' },
  
  // 仪表板端点
  { method: 'GET', path: '/api/dashboard/summary?start_date=2025-06-01&end_date=2025-06-30', description: '仪表板汇总' },
  { method: 'GET', path: '/api/dashboard/kpis?start_date=2025-06-01&end_date=2025-06-30', description: 'KPI指标' },
  
  // 趋势端点
  { method: 'GET', path: '/api/trends/ratio?start_date=2025-06-01&end_date=2025-06-30', description: '产销比趋势' },
  { method: 'GET', path: '/api/trends/sales-price?start_date=2025-06-01&end_date=2025-06-30', description: '销售价格趋势' },
  
  // 兼容性端点（重定向测试）
  { method: 'GET', path: '/api/products', description: '产品列表（重定向）' },
  { method: 'GET', path: '/api/price-changes?start_date=2025-06-01&end_date=2025-06-30', description: '价格变动（重定向）' }
];

/**
 * 测试单个端点
 */
async function testEndpoint(endpoint) {
  const url = `${BASE_URL}${endpoint.path}`;
  
  try {
    console.log(`🔄 测试: ${endpoint.description} (${endpoint.method} ${endpoint.path})`);
    
    const response = await fetch(url, {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });
    
    const statusCode = response.status;
    const statusText = response.statusText;
    
    if (statusCode >= 200 && statusCode < 400) {
      console.log(`✅ 成功: ${endpoint.description} - ${statusCode} ${statusText}`);
      
      // 尝试解析JSON响应
      try {
        const data = await response.json();
        if (data.success !== undefined) {
          console.log(`   📊 响应格式: ${data.success ? '成功' : '失败'}`);
          if (data.data && typeof data.data === 'object') {
            const keys = Object.keys(data.data);
            console.log(`   📋 数据字段: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
          }
        }
      } catch (jsonError) {
        console.log(`   ⚠️  非JSON响应或解析失败`);
      }
      
      return { success: true, status: statusCode, endpoint };
    } else {
      console.log(`❌ 失败: ${endpoint.description} - ${statusCode} ${statusText}`);
      return { success: false, status: statusCode, endpoint, error: statusText };
    }
    
  } catch (error) {
    console.log(`💥 错误: ${endpoint.description} - ${error.message}`);
    return { success: false, endpoint, error: error.message };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始API重构验证测试...\n');
  console.log(`📍 测试目标: ${BASE_URL}`);
  console.log(`📊 测试端点数量: ${TEST_ENDPOINTS.length}\n`);
  
  const results = [];
  let successCount = 0;
  let failureCount = 0;
  
  for (const endpoint of TEST_ENDPOINTS) {
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    if (result.success) {
      successCount++;
    } else {
      failureCount++;
    }
    
    // 添加延迟避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
    console.log(''); // 空行分隔
  }
  
  // 输出测试总结
  console.log('📋 测试总结:');
  console.log(`✅ 成功: ${successCount}/${TEST_ENDPOINTS.length}`);
  console.log(`❌ 失败: ${failureCount}/${TEST_ENDPOINTS.length}`);
  console.log(`📈 成功率: ${Math.round((successCount / TEST_ENDPOINTS.length) * 100)}%\n`);
  
  // 输出失败的端点
  const failures = results.filter(r => !r.success);
  if (failures.length > 0) {
    console.log('❌ 失败的端点:');
    failures.forEach(failure => {
      console.log(`   - ${failure.endpoint.description}: ${failure.error || failure.status}`);
    });
    console.log('');
  }
  
  // 输出重定向端点测试结果
  const redirectEndpoints = results.filter(r => 
    r.endpoint.path.includes('/api/products') || 
    r.endpoint.path.includes('/api/price-changes')
  );
  
  if (redirectEndpoints.length > 0) {
    console.log('🔄 重定向端点测试:');
    redirectEndpoints.forEach(result => {
      const status = result.status === 301 ? '✅ 正确重定向' : `❌ 状态码: ${result.status}`;
      console.log(`   - ${result.endpoint.description}: ${status}`);
    });
    console.log('');
  }
  
  console.log('🎉 API重构验证测试完成!');
  
  return {
    total: TEST_ENDPOINTS.length,
    success: successCount,
    failure: failureCount,
    successRate: Math.round((successCount / TEST_ENDPOINTS.length) * 100),
    results
  };
}

// 如果直接运行此脚本
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, testEndpoint, TEST_ENDPOINTS };
}
