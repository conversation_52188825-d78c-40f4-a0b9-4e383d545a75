-- 价格预警系统扩展Schema
-- 可选择性添加到主schema中

-- 价格预警表，存储系统生成的价格预警
CREATE TABLE IF NOT EXISTS PriceAlerts (
    alert_id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    product_name TEXT NOT NULL,
    alert_type TEXT NOT NULL, -- 'PRICE_DROP', 'PRICE_RISE', 'HIGH_VOLATILITY'
    alert_level TEXT NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'
    alert_date TEXT NOT NULL,
    current_price REAL NOT NULL,
    previous_price REAL,
    price_difference REAL,
    change_percentage REAL,
    alert_message TEXT NOT NULL,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_at TEXT,
    acknowledged_by TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES Products(product_id)
);

-- 预警确认记录表，跟踪预警处理历史
CREATE TABLE IF NOT EXISTS AlertAcknowledgments (
    ack_id INTEGER PRIMARY KEY AUTOINCREMENT,
    alert_id INTEGER NOT NULL,
    acknowledged_by TEXT NOT NULL,
    acknowledged_at TEXT DEFAULT CURRENT_TIMESTAMP,
    acknowledgment_note TEXT,
    FOREIGN KEY (alert_id) REFERENCES PriceAlerts(alert_id)
);

-- 为预警表创建索引
CREATE INDEX IF NOT EXISTS idx_pricealerts_date ON PriceAlerts(alert_date);
CREATE INDEX IF NOT EXISTS idx_pricealerts_product_id ON PriceAlerts(product_id);
CREATE INDEX IF NOT EXISTS idx_pricealerts_level ON PriceAlerts(alert_level);
CREATE INDEX IF NOT EXISTS idx_pricealerts_acknowledged ON PriceAlerts(acknowledged);