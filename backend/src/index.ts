/**
 * Spring Snow Food Analysis System - Backend API
 * 主入口文件 - 重构版本
 *
 * 版本: 2.0.0
 * 架构: 模块化微服务架构
 */

import { Hono } from 'hono';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';

// 导入中间件
import { corsMiddleware } from './middleware/cors';
import { errorHandler } from './middleware/errorHandler';

// 导入路由模块
import inventoryRoutes from './routes/inventory';
import productionRoutes from './routes/production';
import salesRoutes from './routes/sales';
import pricingRoutes from './routes/pricing';
import dashboardRoutes from './routes/dashboard';
import authRoutes from './routes/auth';
import trendsRoutes from './routes/trends';
import systemRoutes from './routes/system';

// 导入类型定义
import type { Bindings } from './types/api';

// 创建Hono应用实例
const app = new Hono<{ Bindings: Bindings }>();

// --- 全局中间件配置 ---

// 请求日志中间件
app.use('*', logger());

// JSON格式化中间件
app.use('*', prettyJSON());

// CORS中间件
app.use('*', corsMiddleware());

// --- 健康检查端点 ---
app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    architecture: 'modular',
    modules: [
      'inventory', 'production', 'sales',
      'pricing', 'dashboard', 'auth',
      'trends', 'system'
    ]
  });
});

// --- API路由挂载 ---

// 库存管理路由
app.route('/api/inventory', inventoryRoutes);

// 生产管理路由
app.route('/api/production', productionRoutes);

// 销售管理路由
app.route('/api/sales', salesRoutes);

// 价格监控路由
app.route('/api/pricing', pricingRoutes);

// 仪表板路由
app.route('/api/dashboard', dashboardRoutes);

// 认证路由
app.route('/api/auth', authRoutes);

// 趋势分析路由
app.route('/api/trends', trendsRoutes);

// 系统管理路由
app.route('/api/system', systemRoutes);

// --- 兼容性路由（保持向后兼容） ---

// 重定向旧的认证端点
app.post('/api/register', (c) => c.redirect('/api/auth/register', 301));
app.post('/api/login', (c) => c.redirect('/api/auth/login', 301));

// 重定向旧的产品端点
app.get('/api/products', (c) => c.redirect('/api/system/products', 301));

// 重定向旧的价格端点（保持查询参数）
app.get('/api/price-changes', (c) => {
  const url = new URL(c.req.url);
  const searchParams = url.searchParams.toString();
  const newUrl = `/api/pricing/changes${searchParams ? '?' + searchParams : ''}`;
  return c.redirect(newUrl, 301);
});
app.get('/api/price-trends', (c) => {
  const url = new URL(c.req.url);
  const searchParams = url.searchParams.toString();
  const newUrl = `/api/pricing/trends${searchParams ? '?' + searchParams : ''}`;
  return c.redirect(newUrl, 301);
});

// --- 错误处理 ---

// 404处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'API端点未找到',
    message: '请检查请求路径是否正确',
    available_endpoints: {
      inventory: '/api/inventory/*',
      production: '/api/production/*',
      sales: '/api/sales/*',
      pricing: '/api/pricing/*',
      dashboard: '/api/dashboard/*',
      auth: '/api/auth/*',
      trends: '/api/trends/*',
      system: '/api/system/*'
    }
  }, 404);
});

// 全局错误处理
app.onError(errorHandler);

// 导出应用
export default app;
