/**
 * 数据库相关类型定义
 * 数据表结构和查询结果类型
 */

// DailyMetrics表结构
export interface DailyMetricsRow {
  id?: number;
  product_id: number;
  record_date: string;
  production_volume?: number;
  sales_volume?: number;
  inventory_level?: number;
  average_price?: number;
  sales_amount?: number;
  created_at?: string;
  updated_at?: string;
}

// Products表结构
export interface ProductRow {
  product_id: number;
  product_name: string;
  category?: string;
  unit?: string;
  created_at?: string;
  updated_at?: string;
}

// PriceAdjustments表结构
export interface PriceAdjustmentRow {
  id?: number;
  product_id: number;
  product_name: string;
  adjustment_date: string;
  previous_price: number;
  current_price: number;
  price_difference: number;
  change_percentage?: number;
  created_at?: string;
}

// PriceAlerts表结构
export interface PriceAlertRow {
  id?: number;
  product_id: number;
  product_name: string;
  alert_type: string;
  alert_level: string;
  alert_date: string;
  current_price: number;
  previous_price?: number;
  price_difference?: number;
  change_percentage?: number;
  threshold_value?: number;
  alert_message: string;
  acknowledged: boolean;
  acknowledged_at?: string;
  created_at?: string;
}

// AlertConfigurations表结构
export interface AlertConfigurationRow {
  id?: number;
  alert_type: string;
  alert_level: string;
  threshold_value: number;
  threshold_percentage?: number;
  is_active: boolean;
  description?: string;
  created_at?: string;
  updated_at?: string;
}

// Users表结构
export interface UserRow {
  id?: number;
  username: string;
  password_hash: string;
  created_at?: string;
  updated_at?: string;
}

// 数据库查询结果类型
export interface QueryResult<T = any> {
  results: T[];
  success: boolean;
  meta?: {
    changes?: number;
    duration?: number;
    last_row_id?: number;
    rows_read?: number;
    rows_written?: number;
    size_after?: number;
  };
}

// 批量操作结果
export interface BatchResult {
  success: boolean;
  count: number;
  duration: number;
  results: any[];
}

// 数据库连接配置
export interface DatabaseConfig {
  database: any; // D1Database类型
  timeout?: number;
  retries?: number;
}

// 查询构建器接口
export interface QueryBuilder {
  select(fields: string[]): QueryBuilder;
  from(table: string): QueryBuilder;
  where(condition: string): QueryBuilder;
  join(table: string, condition: string): QueryBuilder;
  orderBy(field: string, direction?: 'ASC' | 'DESC'): QueryBuilder;
  limit(count: number): QueryBuilder;
  build(): string;
}

// 数据过滤选项
export interface FilterOptions {
  includeFreshProducts?: boolean;
  includeByProducts?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  productIds?: number[];
  categories?: string[];
}

// 聚合查询结果
export interface AggregateResult {
  count: number;
  sum: number;
  avg: number;
  min: number;
  max: number;
}

// 分页查询参数
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

// 分页查询结果
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
