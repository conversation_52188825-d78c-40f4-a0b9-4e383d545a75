/**
 * API类型定义
 * 统一的API响应格式和业务数据类型
 */

import type { D1Database } from '@cloudflare/workers-types';

// Cloudflare环境变量绑定
export type Bindings = {
  DB: D1Database;
};

// 统一API响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: string;
}

// 价格相关接口
export interface DailyDropRow {
  product_id: number;
  product_name: string;
  current_price: number;
  previous_price: number;
  price_difference: number;
  adjustment_date: string;
  change_percentage: number;
}

export interface ConsecutiveDropRow {
  adjustment_date: string;
  current_price: number;
  previous_price: number;
  price_difference: number;
  product_name: string;
}

export interface VolatilityRow {
  current_price: number;
  adjustment_date: string;
  product_name: string;
}

export interface ProductIdRow {
  product_id: number;
}

export interface PriceTrendsSummary {
  total_products: number;
  start_date: string;
  end_date: string;
  avg_price: number;
  min_price: number;
  max_price: number;
  total_adjustments: number;
}

// 生产相关接口
export interface DailyRatio {
  date: string;
  sales_volume: number;
  production_volume: number;
  ratio: number;
}

export interface RatioStatistics {
  avg_ratio: number;
  min_ratio: number;
  max_ratio: number;
  total_days: number;
  total_sales: number;
  total_production: number;
  calculation_method: string;
}

export interface AggregateMetrics {
  totalSales: number;
  totalProduction: number;
  totalSalesAmount: number;
  totalProducts: number;
}

// 库存相关接口
export interface InventorySummary {
  total_inventory: number;
  product_count: number;
  avg_price: number;
  top15_total?: number;
  top15_percentage?: number;
}

export interface InventoryTrend {
  date: string;
  inventory: number;
  change?: number;
}

export interface TopProduct {
  rank: number;
  product_name: string;
  inventory: number;
  unit: string;
}

// 销售相关接口
export interface SalesData {
  date: string;
  sales_volume: number;
  sales_amount: number;
  average_price: number;
}

// 查询参数接口
export interface DateRangeQuery {
  start_date?: string;
  end_date?: string;
  date?: string;
}

export interface InventoryQuery extends DateRangeQuery {
  limit?: string;
  product_filter?: string;
}

export interface ProductionQuery extends DateRangeQuery {
  period?: string;
}

export interface PricingQuery extends DateRangeQuery {
  product_id?: string;
  product_name?: string;
  min_price_diff?: string;
  alert_type?: string;
  alert_level?: string;
  acknowledged?: string;
  limit?: string;
}

// 数据验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// 数据库查询结果接口
export interface DatabaseQueryResult<T = any> {
  results: T[];
  success: boolean;
  meta?: any;
}
