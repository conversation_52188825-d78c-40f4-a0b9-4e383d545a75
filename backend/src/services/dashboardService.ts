/**
 * 仪表板业务服务
 * 处理仪表板汇总数据的业务逻辑
 */

import type { D1Database } from '@cloudflare/workers-types';
import { DatabaseManager } from '../utils/database';
import { ProductFilter } from '../utils/filters';
import { DatabaseError } from '../middleware/errorHandler';
import { InventoryService } from './inventoryService';
import { ProductionService } from './productionService';
import { SalesService } from './salesService';
import type { AggregateMetrics } from '../types/api';

export class DashboardService {
  private db: DatabaseManager;
  private inventoryService: InventoryService;
  private productionService: ProductionService;
  private salesService: SalesService;

  constructor(database: D1Database) {
    this.db = new DatabaseManager(database);
    this.inventoryService = new InventoryService(database);
    this.productionService = new ProductionService(database);
    this.salesService = new SalesService(database);
  }

  /**
   * 获取仪表板汇总数据
   */
  async getSummary(startDate: string, endDate: string): Promise<{
    total_inventory: number;
    total_sales: number;
    total_production: number;
    total_sales_amount: number;
    avg_production_ratio: number;
    total_products: number;
    inventory_turnover: number;
    avg_price: number;
    period_days: number;
  }> {
    try {
      // 并行获取各项数据
      const [
        inventorySummary,
        productionStats,
        salesSummary,
        aggregateMetrics
      ] = await Promise.all([
        this.getInventoryMetrics(endDate),
        this.productionService.calculateStatistics(startDate, endDate),
        this.salesService.getSalesSummary(startDate, endDate),
        this.getAggregateMetrics(startDate, endDate)
      ]);

      // 计算期间天数
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      const periodDays = Math.ceil((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      // 计算库存周转率（销售量/平均库存）
      const inventoryTurnover = inventorySummary.total_inventory > 0 
        ? (salesSummary.total_sales_volume / inventorySummary.total_inventory) * 100 
        : 0;

      return {
        total_inventory: inventorySummary.total_inventory,
        total_sales: salesSummary.total_sales_volume,
        total_production: productionStats.total_production,
        total_sales_amount: salesSummary.total_sales_amount,
        avg_production_ratio: productionStats.avg_ratio,
        total_products: aggregateMetrics.totalProducts,
        inventory_turnover: Math.round(inventoryTurnover * 100) / 100,
        avg_price: salesSummary.average_price,
        period_days: periodDays
      };

    } catch (error) {
      console.error('获取仪表板汇总数据失败:', error);
      throw new DatabaseError('获取仪表板汇总数据失败', error);
    }
  }

  /**
   * 获取库存指标
   */
  private async getInventoryMetrics(date: string): Promise<{
    total_inventory: number;
    product_count: number;
  }> {
    try {
      const filterClause = ProductFilter.getInventoryFilter('p');

      const query = `
        SELECT 
          COALESCE(SUM(dm.inventory_level), 0) as total_inventory,
          COUNT(DISTINCT dm.product_id) as product_count
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date = ?
          AND dm.inventory_level IS NOT NULL
          AND dm.inventory_level > 0
          AND ${filterClause}
      `;

      const result = await this.db.queryFirst<{
        total_inventory: number;
        product_count: number;
      }>(query, [date]);

      return {
        total_inventory: (result?.total_inventory || 0) / 1000, // 转换为吨
        product_count: result?.product_count || 0
      };

    } catch (error) {
      console.error('获取库存指标失败:', error);
      throw new DatabaseError('获取库存指标失败', error);
    }
  }

  /**
   * 获取聚合指标
   */
  private async getAggregateMetrics(startDate: string, endDate: string): Promise<AggregateMetrics> {
    try {
      const filterClause = ProductFilter.getCompleteFilter(true);

      const query = `
        SELECT 
          COALESCE(SUM(dm.sales_volume), 0) as totalSales,
          COALESCE(SUM(dm.production_volume), 0) as totalProduction,
          COALESCE(SUM(dm.sales_amount), 0) as totalSalesAmount,
          COUNT(DISTINCT dm.product_id) as totalProducts
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND ${filterClause}
      `;

      const result = await this.db.queryFirst<AggregateMetrics>(query, [startDate, endDate]);

      return result || {
        totalSales: 0,
        totalProduction: 0,
        totalSalesAmount: 0,
        totalProducts: 0
      };

    } catch (error) {
      console.error('获取聚合指标失败:', error);
      throw new DatabaseError('获取聚合指标失败', error);
    }
  }

  /**
   * 获取关键绩效指标（KPI）
   */
  async getKPIs(startDate: string, endDate: string): Promise<{
    sales_growth: number;
    production_efficiency: number;
    inventory_turnover: number;
    price_stability: number;
    data_completeness: number;
  }> {
    try {
      // 计算销售增长率（需要对比前一期间）
      const salesGrowth = await this.calculateSalesGrowth(startDate, endDate);
      
      // 计算生产效率（产销比）
      const productionStats = await this.productionService.calculateStatistics(startDate, endDate);
      const productionEfficiency = productionStats.avg_ratio;

      // 计算库存周转率
      const inventoryTurnover = await this.calculateInventoryTurnover(startDate, endDate);

      // 计算价格稳定性（价格波动的倒数）
      const priceStability = await this.calculatePriceStability(startDate, endDate);

      // 计算数据完整性
      const dataCompleteness = await this.calculateDataCompleteness(startDate, endDate);

      return {
        sales_growth: Math.round(salesGrowth * 100) / 100,
        production_efficiency: Math.round(productionEfficiency * 100) / 100,
        inventory_turnover: Math.round(inventoryTurnover * 100) / 100,
        price_stability: Math.round(priceStability * 100) / 100,
        data_completeness: Math.round(dataCompleteness * 100) / 100
      };

    } catch (error) {
      console.error('获取KPI指标失败:', error);
      throw new DatabaseError('获取KPI指标失败', error);
    }
  }

  /**
   * 计算销售增长率
   */
  private async calculateSalesGrowth(startDate: string, endDate: string): Promise<number> {
    try {
      // 计算当前期间的销售总量
      const currentSales = await this.salesService.getSalesSummary(startDate, endDate);
      
      // 计算前一期间的日期范围
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      const periodDays = Math.ceil((endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24));
      
      const prevEndDate = new Date(startDateObj.getTime() - 24 * 60 * 60 * 1000);
      const prevStartDate = new Date(prevEndDate.getTime() - periodDays * 24 * 60 * 60 * 1000);
      
      const prevSales = await this.salesService.getSalesSummary(
        prevStartDate.toISOString().split('T')[0],
        prevEndDate.toISOString().split('T')[0]
      );

      if (prevSales.total_sales_volume === 0) return 0;

      return ((currentSales.total_sales_volume - prevSales.total_sales_volume) / prevSales.total_sales_volume) * 100;

    } catch (error) {
      console.error('计算销售增长率失败:', error);
      return 0;
    }
  }

  /**
   * 计算库存周转率
   */
  private async calculateInventoryTurnover(startDate: string, endDate: string): Promise<number> {
    try {
      const salesSummary = await this.salesService.getSalesSummary(startDate, endDate);
      const inventoryMetrics = await this.getInventoryMetrics(endDate);

      if (inventoryMetrics.total_inventory === 0) return 0;

      return (salesSummary.total_sales_volume / inventoryMetrics.total_inventory) * 100;

    } catch (error) {
      console.error('计算库存周转率失败:', error);
      return 0;
    }
  }

  /**
   * 计算价格稳定性
   */
  private async calculatePriceStability(startDate: string, endDate: string): Promise<number> {
    try {
      const query = `
        SELECT 
          AVG(dm.average_price) as avg_price,
          COUNT(*) as price_count
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.average_price IS NOT NULL
          AND dm.average_price > 0
          AND ${ProductFilter.getCompleteFilter(true)}
      `;

      const result = await this.db.queryFirst<{
        avg_price: number;
        price_count: number;
      }>(query, [startDate, endDate]);

      if (!result || result.price_count === 0) return 100;

      // 计算价格标准差
      const varianceQuery = `
        SELECT 
          AVG(POWER(dm.average_price - ?, 2)) as variance
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.average_price IS NOT NULL
          AND dm.average_price > 0
          AND ${ProductFilter.getCompleteFilter(true)}
      `;

      const varianceResult = await this.db.queryFirst<{ variance: number }>(
        varianceQuery, 
        [result.avg_price, startDate, endDate]
      );

      const standardDeviation = Math.sqrt(varianceResult?.variance || 0);
      const coefficientOfVariation = result.avg_price > 0 ? (standardDeviation / result.avg_price) * 100 : 0;

      // 价格稳定性 = 100 - 变异系数
      return Math.max(0, 100 - coefficientOfVariation);

    } catch (error) {
      console.error('计算价格稳定性失败:', error);
      return 100;
    }
  }

  /**
   * 计算数据完整性
   */
  private async calculateDataCompleteness(startDate: string, endDate: string): Promise<number> {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_records,
          COUNT(CASE WHEN dm.sales_volume IS NOT NULL AND dm.sales_volume > 0 THEN 1 END) as sales_records,
          COUNT(CASE WHEN dm.production_volume IS NOT NULL AND dm.production_volume > 0 THEN 1 END) as production_records,
          COUNT(CASE WHEN dm.inventory_level IS NOT NULL AND dm.inventory_level > 0 THEN 1 END) as inventory_records,
          COUNT(CASE WHEN dm.average_price IS NOT NULL AND dm.average_price > 0 THEN 1 END) as price_records
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND ${ProductFilter.getCompleteFilter(true)}
      `;

      const result = await this.db.queryFirst<{
        total_records: number;
        sales_records: number;
        production_records: number;
        inventory_records: number;
        price_records: number;
      }>(query, [startDate, endDate]);

      if (!result || result.total_records === 0) return 0;

      // 计算各字段的完整性百分比
      const salesCompleteness = (result.sales_records / result.total_records) * 100;
      const productionCompleteness = (result.production_records / result.total_records) * 100;
      const inventoryCompleteness = (result.inventory_records / result.total_records) * 100;
      const priceCompleteness = (result.price_records / result.total_records) * 100;

      // 返回平均完整性
      return (salesCompleteness + productionCompleteness + inventoryCompleteness + priceCompleteness) / 4;

    } catch (error) {
      console.error('计算数据完整性失败:', error);
      return 0;
    }
  }
}
