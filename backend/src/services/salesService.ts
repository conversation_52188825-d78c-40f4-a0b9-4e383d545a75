/**
 * 销售业务服务
 * 处理销售相关的业务逻辑
 */

import type { D1Database } from '@cloudflare/workers-types';
import { DatabaseManager } from '../utils/database';
import { ProductFilter } from '../utils/filters';
import { DatabaseError } from '../middleware/errorHandler';
import type { SalesData } from '../types/api';

export class SalesService {
  private db: DatabaseManager;

  constructor(database: D1Database) {
    this.db = new DatabaseManager(database);
  }

  /**
   * 获取销售详情数据
   */
  async getSalesData(startDate: string, endDate: string): Promise<SalesData[]> {
    try {
      const filterClause = ProductFilter.getSalesFilter('p');

      const query = `
        SELECT 
          dm.record_date as date,
          COALESCE(SUM(dm.sales_volume), 0) as sales_volume,
          COALESCE(SUM(dm.sales_amount), 0) as sales_amount,
          CASE 
            WHEN SUM(dm.sales_volume) > 0 
            THEN ROUND(SUM(dm.sales_amount) / SUM(dm.sales_volume), 2)
            ELSE 0 
          END as average_price
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.sales_volume IS NOT NULL
          AND dm.sales_volume > 0
          AND ${filterClause}
        GROUP BY dm.record_date
        ORDER BY dm.record_date ASC
      `;

      const result = await this.db.query<{
        date: string;
        sales_volume: number;
        sales_amount: number;
        average_price: number;
      }>(query, [startDate, endDate]);

      return result.results.map(row => ({
        date: row.date,
        sales_volume: row.sales_volume || 0,
        sales_amount: row.sales_amount || 0,
        average_price: row.average_price || 0
      }));

    } catch (error) {
      console.error('获取销售数据失败:', error);
      throw new DatabaseError('获取销售数据失败', error);
    }
  }

  /**
   * 获取销售价格趋势数据
   */
  async getSalesPriceTrends(startDate: string, endDate: string): Promise<{
    date: string;
    sales_volume: number;
    average_price: number;
  }[]> {
    try {
      const filterClause = ProductFilter.getCompleteFilter(true);

      const query = `
        SELECT DISTINCT
          dm.record_date as date,
          COALESCE(SUM(dm.sales_volume), 0) as sales_volume,
          CASE 
            WHEN SUM(dm.sales_volume) > 0 
            THEN ROUND(SUM(dm.sales_amount) / SUM(dm.sales_volume), 2)
            ELSE 0 
          END as average_price
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.sales_volume IS NOT NULL
          AND dm.sales_volume > 0
          AND dm.sales_amount IS NOT NULL
          AND dm.sales_amount > 0
          AND ${filterClause}
        GROUP BY dm.record_date
        ORDER BY dm.record_date ASC
      `;

      const result = await this.db.query<{
        date: string;
        sales_volume: number;
        average_price: number;
      }>(query, [startDate, endDate]);

      return result.results;

    } catch (error) {
      console.error('获取销售价格趋势失败:', error);
      throw new DatabaseError('获取销售价格趋势失败', error);
    }
  }

  /**
   * 获取销售汇总统计
   */
  async getSalesSummary(startDate: string, endDate: string): Promise<{
    total_sales_volume: number;
    total_sales_amount: number;
    average_price: number;
    total_days: number;
    daily_average_volume: number;
    daily_average_amount: number;
  }> {
    try {
      const salesData = await this.getSalesData(startDate, endDate);

      if (salesData.length === 0) {
        return {
          total_sales_volume: 0,
          total_sales_amount: 0,
          average_price: 0,
          total_days: 0,
          daily_average_volume: 0,
          daily_average_amount: 0
        };
      }

      const totalSalesVolume = salesData.reduce((sum, day) => sum + day.sales_volume, 0);
      const totalSalesAmount = salesData.reduce((sum, day) => sum + day.sales_amount, 0);
      const averagePrice = totalSalesVolume > 0 ? totalSalesAmount / totalSalesVolume : 0;
      const totalDays = salesData.length;

      return {
        total_sales_volume: totalSalesVolume,
        total_sales_amount: totalSalesAmount,
        average_price: Math.round(averagePrice * 100) / 100,
        total_days: totalDays,
        daily_average_volume: Math.round((totalSalesVolume / totalDays) * 100) / 100,
        daily_average_amount: Math.round((totalSalesAmount / totalDays) * 100) / 100
      };

    } catch (error) {
      console.error('获取销售汇总统计失败:', error);
      throw new DatabaseError('获取销售汇总统计失败', error);
    }
  }

  /**
   * 获取产品销售排名
   */
  async getProductSalesRanking(startDate: string, endDate: string, limit: number = 20): Promise<{
    product_name: string;
    total_sales_volume: number;
    total_sales_amount: number;
    average_price: number;
    rank: number;
  }[]> {
    try {
      const filterClause = ProductFilter.getSalesFilter('p');

      const query = `
        SELECT 
          p.product_name,
          COALESCE(SUM(dm.sales_volume), 0) as total_sales_volume,
          COALESCE(SUM(dm.sales_amount), 0) as total_sales_amount,
          CASE 
            WHEN SUM(dm.sales_volume) > 0 
            THEN ROUND(SUM(dm.sales_amount) / SUM(dm.sales_volume), 2)
            ELSE 0 
          END as average_price
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.sales_volume IS NOT NULL
          AND dm.sales_volume > 0
          AND ${filterClause}
        GROUP BY p.product_id, p.product_name
        ORDER BY total_sales_volume DESC
        LIMIT ?
      `;

      const result = await this.db.query<{
        product_name: string;
        total_sales_volume: number;
        total_sales_amount: number;
        average_price: number;
      }>(query, [startDate, endDate, limit]);

      return result.results.map((product, index) => ({
        ...product,
        rank: index + 1
      }));

    } catch (error) {
      console.error('获取产品销售排名失败:', error);
      throw new DatabaseError('获取产品销售排名失败', error);
    }
  }

  /**
   * 获取销售趋势分析
   */
  async getSalesTrendAnalysis(startDate: string, endDate: string): Promise<{
    trend_direction: 'up' | 'down' | 'stable';
    growth_rate: number;
    peak_date: string;
    peak_volume: number;
    low_date: string;
    low_volume: number;
    volatility: number;
  }> {
    try {
      const salesData = await this.getSalesData(startDate, endDate);

      if (salesData.length < 2) {
        return {
          trend_direction: 'stable',
          growth_rate: 0,
          peak_date: '',
          peak_volume: 0,
          low_date: '',
          low_volume: 0,
          volatility: 0
        };
      }

      // 计算趋势方向和增长率
      const firstWeekAvg = salesData.slice(0, 7).reduce((sum, day) => sum + day.sales_volume, 0) / 7;
      const lastWeekAvg = salesData.slice(-7).reduce((sum, day) => sum + day.sales_volume, 0) / 7;
      const growthRate = firstWeekAvg > 0 ? ((lastWeekAvg - firstWeekAvg) / firstWeekAvg) * 100 : 0;

      let trendDirection: 'up' | 'down' | 'stable' = 'stable';
      if (Math.abs(growthRate) > 5) {
        trendDirection = growthRate > 0 ? 'up' : 'down';
      }

      // 找到峰值和低值
      const peakDay = salesData.reduce((max, day) => 
        day.sales_volume > max.sales_volume ? day : max
      );
      const lowDay = salesData.reduce((min, day) => 
        day.sales_volume < min.sales_volume ? day : min
      );

      // 计算波动性（标准差）
      const avgVolume = salesData.reduce((sum, day) => sum + day.sales_volume, 0) / salesData.length;
      const variance = salesData.reduce((sum, day) => 
        sum + Math.pow(day.sales_volume - avgVolume, 2), 0
      ) / salesData.length;
      const volatility = Math.sqrt(variance);

      return {
        trend_direction: trendDirection,
        growth_rate: Math.round(growthRate * 100) / 100,
        peak_date: peakDay.date,
        peak_volume: peakDay.sales_volume,
        low_date: lowDay.date,
        low_volume: lowDay.sales_volume,
        volatility: Math.round(volatility * 100) / 100
      };

    } catch (error) {
      console.error('获取销售趋势分析失败:', error);
      throw new DatabaseError('获取销售趋势分析失败', error);
    }
  }

  /**
   * 获取简化销售数据（调试用）
   */
  async getSimpleSalesData(startDate: string, endDate: string): Promise<{
    date: string;
    sales_volume: number;
    sales_amount: number;
  }[]> {
    try {
      const query = `
        SELECT 
          dm.record_date as date,
          COALESCE(SUM(dm.sales_volume), 0) as sales_volume,
          COALESCE(SUM(dm.sales_amount), 0) as sales_amount
        FROM DailyMetrics dm
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.sales_volume IS NOT NULL
          AND dm.sales_volume > 0
        GROUP BY dm.record_date
        ORDER BY dm.record_date ASC
      `;

      const result = await this.db.query<{
        date: string;
        sales_volume: number;
        sales_amount: number;
      }>(query, [startDate, endDate]);

      return result.results;

    } catch (error) {
      console.error('获取简化销售数据失败:', error);
      throw new DatabaseError('获取简化销售数据失败', error);
    }
  }

  /**
   * 获取原始销售数据（调试用）
   */
  async getRawSalesData(startDate: string, endDate: string, limit: number = 10): Promise<{
    record_date: string;
    sales_volume: number;
    sales_amount: number;
    amount_type: string;
  }[]> {
    try {
      const query = `
        SELECT
          dm.record_date,
          dm.sales_volume,
          dm.sales_amount,
          typeof(dm.sales_amount) as amount_type
        FROM DailyMetrics dm
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.sales_volume IS NOT NULL
          AND dm.sales_volume > 0
        ORDER BY dm.record_date ASC
        LIMIT ?
      `;

      const result = await this.db.query<{
        record_date: string;
        sales_volume: number;
        sales_amount: number;
        amount_type: string;
      }>(query, [startDate, endDate, limit]);

      return result.results;

    } catch (error) {
      console.error('获取原始销售数据失败:', error);
      throw new DatabaseError('获取原始销售数据失败', error);
    }
  }
}
