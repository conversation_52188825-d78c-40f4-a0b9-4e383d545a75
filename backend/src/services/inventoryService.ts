/**
 * 库存业务服务
 * 处理库存相关的业务逻辑
 */

import type { D1Database } from '@cloudflare/workers-types';
import { DatabaseManager } from '../utils/database';
import { ProductFilter, DateFilter } from '../utils/filters';
import { DatabaseError } from '../middleware/errorHandler';
import type { 
  InventorySummary, 
  InventoryTrend, 
  TopProduct,
  InventoryQuery 
} from '../types/api';

export class InventoryService {
  private db: DatabaseManager;

  constructor(database: D1Database) {
    this.db = new DatabaseManager(database);
  }

  /**
   * 获取库存汇总信息
   */
  async getSummary(options: InventoryQuery): Promise<InventorySummary> {
    try {
      const { date, start_date, end_date } = options;
      
      // 确定查询日期范围
      let finalStartDate: string | undefined;
      let finalEndDate: string;

      if (start_date && end_date) {
        finalStartDate = start_date;
        finalEndDate = end_date;
      } else if (date) {
        finalEndDate = date;
      } else {
        // 使用最新可用日期
        const latestDate = await this.getLatestInventoryDate();
        finalEndDate = latestDate;
      }

      // 获取过滤条件
      const filterClause = ProductFilter.getInventoryFilter('p');

      // 查询期间范围数据（如果有开始日期）
      let rangeResult: { product_count: number; avg_price: number } | null = null;
      if (finalStartDate) {
        const rangeQuery = `
          SELECT 
            COUNT(DISTINCT dm.product_id) as product_count,
            AVG(dm.average_price) as avg_price
          FROM DailyMetrics dm
          JOIN Products p ON dm.product_id = p.product_id
          WHERE dm.record_date BETWEEN ? AND ?
            AND dm.inventory_level IS NOT NULL
            AND dm.inventory_level > 0
            AND ${filterClause}
        `;
        rangeResult = await this.db.queryFirst(rangeQuery, [finalStartDate, finalEndDate]);
      }

      // 查询期末库存快照
      const snapshotQuery = `
        SELECT SUM(dm.inventory_level) as total_inventory
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date = ?
          AND dm.inventory_level IS NOT NULL
          AND dm.inventory_level > 0
          AND ${filterClause}
      `;
      const snapshotResult = await this.db.queryFirst<{ total_inventory: number }>(
        snapshotQuery, 
        [finalEndDate]
      );

      // 查询TOP15库存总量
      const top15Query = `
        SELECT SUM(inventory_level) as top15_total
        FROM (
          SELECT dm.inventory_level
          FROM DailyMetrics dm
          JOIN Products p ON dm.product_id = p.product_id
          WHERE dm.record_date = ?
            AND dm.inventory_level IS NOT NULL
            AND dm.inventory_level > 0
            AND ${filterClause}
          ORDER BY dm.inventory_level DESC
          LIMIT 15
        )
      `;
      const top15Result = await this.db.queryFirst<{ top15_total: number }>(
        top15Query, 
        [finalEndDate]
      );

      const totalInventory = snapshotResult?.total_inventory || 0;
      const top15Total = top15Result?.top15_total || 0;
      const top15Percentage = totalInventory > 0 ? (top15Total / totalInventory * 100) : 0;

      return {
        total_inventory: totalInventory / 1000, // 转换为吨
        top15_total: top15Total / 1000, // 转换为吨
        top15_percentage: Math.round(top15Percentage * 100) / 100,
        product_count: rangeResult?.product_count || 0,
        avg_price: rangeResult?.avg_price || 0
      };

    } catch (error) {
      console.error('获取库存汇总失败:', error);
      throw new DatabaseError('获取库存汇总失败', error);
    }
  }

  /**
   * 获取未过滤的总库存汇总
   */
  async getTotalSummary(options: { date?: string; end_date?: string }): Promise<{ total_inventory: number; actual_date: string }> {
    try {
      let targetDate = options.end_date || options.date;

      if (!targetDate) {
        targetDate = await this.getLatestInventoryDate();
      }

      const query = `
        SELECT SUM(inventory_level) as total_inventory
        FROM DailyMetrics
        WHERE record_date = ?
          AND inventory_level IS NOT NULL
          AND inventory_level > 0
      `;

      const result = await this.db.queryFirst<{ total_inventory: number }>(query, [targetDate]);
      let totalInventory = result?.total_inventory || 0;

      // 如果没有找到数据，尝试查找最新的可用日期
      if (totalInventory === 0) {
        const latestDate = await this.getLatestInventoryDate();
        if (latestDate !== targetDate) {
          const latestResult = await this.db.queryFirst<{ total_inventory: number }>(query, [latestDate]);
          totalInventory = latestResult?.total_inventory || 0;
          targetDate = latestDate;
        }
      }

      return {
        total_inventory: totalInventory / 1000.0, // 转换为吨单位
        actual_date: targetDate
      };

    } catch (error) {
      console.error('获取总库存汇总失败:', error);
      throw new DatabaseError('获取总库存汇总失败', error);
    }
  }

  /**
   * 获取TOP N库存产品
   */
  async getTopProducts(options: { date?: string; limit?: string; end_date?: string }): Promise<TopProduct[]> {
    try {
      const limit = parseInt(options.limit || '15');
      let targetDate = options.end_date || options.date;

      if (!targetDate) {
        targetDate = await this.getLatestInventoryDate();
      }

      const filterClause = ProductFilter.getInventoryFilter('p');

      const query = `
        SELECT 
          p.product_name,
          dm.inventory_level
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date = ?
          AND dm.inventory_level IS NOT NULL
          AND dm.inventory_level > 0
          AND ${filterClause}
        ORDER BY dm.inventory_level DESC
        LIMIT ?
      `;

      const result = await this.db.query<{ product_name: string; inventory_level: number }>(
        query, 
        [targetDate, limit]
      );

      return result.results.map((product, index) => ({
        rank: index + 1,
        product_name: product.product_name,
        inventory: product.inventory_level / 1000, // 转换为吨
        unit: 'T'
      }));

    } catch (error) {
      console.error('获取TOP库存产品失败:', error);
      throw new DatabaseError('获取TOP库存产品失败', error);
    }
  }

  /**
   * 获取库存分布数据（饼图）
   */
  async getDistribution(options: { date?: string; limit?: string }): Promise<TopProduct[]> {
    try {
      const limit = parseInt(options.limit || '15');
      const targetDate = options.date || await this.getLatestInventoryDate();

      const filterClause = ProductFilter.getInventoryFilter('p');

      const query = `
        SELECT 
          p.product_name,
          dm.inventory_level
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date = ?
          AND dm.inventory_level IS NOT NULL
          AND dm.inventory_level > 0
          AND ${filterClause}
        ORDER BY dm.inventory_level DESC
        LIMIT ?
      `;

      const result = await this.db.query<{ product_name: string; inventory_level: number }>(
        query, 
        [targetDate, limit]
      );

      return result.results.map((product, index) => ({
        rank: index + 1,
        product_name: product.product_name,
        inventory: product.inventory_level / 1000, // 转换为吨
        unit: 'T'
      }));

    } catch (error) {
      console.error('获取库存分布失败:', error);
      throw new DatabaseError('获取库存分布失败', error);
    }
  }

  /**
   * 获取库存趋势数据
   */
  async getTrends(options: { start_date?: string; end_date?: string; product_id?: string }): Promise<InventoryTrend[]> {
    try {
      const { start_date, end_date, product_id } = options;

      if (!start_date || !end_date) {
        throw new Error('start_date and end_date are required');
      }

      let query = `
        SELECT 
          dm.record_date as date,
          SUM(dm.inventory_level) as total_inventory
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND dm.inventory_level IS NOT NULL
          AND dm.inventory_level > 0
      `;

      const params: any[] = [start_date, end_date];

      // 添加产品过滤
      if (product_id) {
        query += ' AND dm.product_id = ?';
        params.push(parseInt(product_id));
      } else {
        // 应用产品过滤逻辑
        const filterClause = ProductFilter.getInventoryFilter('p');
        query += ` AND ${filterClause}`;
      }

      query += `
        GROUP BY dm.record_date
        ORDER BY dm.record_date ASC
      `;

      const result = await this.db.query<{ date: string; total_inventory: number }>(query, params);

      return result.results.map(row => ({
        date: row.date,
        inventory: row.total_inventory / 1000, // 转换为吨
        change: 0 // TODO: 计算变化率
      }));

    } catch (error) {
      console.error('获取库存趋势失败:', error);
      throw new DatabaseError('获取库存趋势失败', error);
    }
  }

  /**
   * 获取最新库存日期
   */
  private async getLatestInventoryDate(): Promise<string> {
    try {
      const query = `
        SELECT record_date
        FROM DailyMetrics
        WHERE inventory_level IS NOT NULL
          AND inventory_level > 0
        ORDER BY record_date DESC
        LIMIT 1
      `;

      const result = await this.db.queryFirst<{ record_date: string }>(query);
      
      if (!result) {
        // 如果没有库存数据，返回当前日期
        return new Date().toISOString().split('T')[0];
      }

      return result.record_date;

    } catch (error) {
      console.error('获取最新库存日期失败:', error);
      return new Date().toISOString().split('T')[0];
    }
  }

  /**
   * 计算库存变化率
   */
  private async calculateInventoryChange(startDate: string, endDate: string): Promise<number> {
    try {
      const startQuery = `
        SELECT SUM(inventory_level) as total
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date = ?
          AND dm.inventory_level IS NOT NULL
          AND dm.inventory_level > 0
          AND ${ProductFilter.getInventoryFilter('p')}
      `;

      const endQuery = `
        SELECT SUM(inventory_level) as total
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date = ?
          AND dm.inventory_level IS NOT NULL
          AND dm.inventory_level > 0
          AND ${ProductFilter.getInventoryFilter('p')}
      `;

      const [startResult, endResult] = await Promise.all([
        this.db.queryFirst<{ total: number }>(startQuery, [startDate]),
        this.db.queryFirst<{ total: number }>(endQuery, [endDate])
      ]);

      const startTotal = startResult?.total || 0;
      const endTotal = endResult?.total || 0;

      if (startTotal === 0) return 0;

      return ((endTotal - startTotal) / startTotal) * 100;

    } catch (error) {
      console.error('计算库存变化率失败:', error);
      return 0;
    }
  }
}
