/**
 * 生产业务服务
 * 处理生产相关的业务逻辑，包括产销比计算
 */

import type { D1Database } from '@cloudflare/workers-types';
import { DatabaseManager } from '../utils/database';
import { ProductFilter } from '../utils/filters';
import { DatabaseError } from '../middleware/errorHandler';
import type { 
  DailyRatio, 
  RatioStatistics, 
  AggregateMetrics,
  ProductionQuery 
} from '../types/api';

export class ProductionService {
  private db: DatabaseManager;

  constructor(database: D1Database) {
    this.db = new DatabaseManager(database);
  }

  /**
   * 计算每日产销比数据
   */
  async calculateDailyRatios(startDate: string, endDate: string): Promise<DailyRatio[]> {
    try {
      const filterClause = ProductFilter.getCompleteFilter(true); // 销售数据包含鲜品

      const query = `
        SELECT 
          dm.record_date as date,
          COALESCE(SUM(dm.sales_volume), 0) as sales_volume,
          COALESCE(SUM(dm.production_volume), 0) as production_volume,
          CASE 
            WHEN SUM(dm.production_volume) > 0 
            THEN ROUND((SUM(dm.sales_volume) / SUM(dm.production_volume)) * 100, 2)
            ELSE 0 
          END as ratio
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND (dm.sales_volume IS NOT NULL OR dm.production_volume IS NOT NULL)
          AND ${filterClause}
        GROUP BY dm.record_date
        ORDER BY dm.record_date ASC
      `;

      const result = await this.db.query<{
        date: string;
        sales_volume: number;
        production_volume: number;
        ratio: number;
      }>(query, [startDate, endDate]);

      return result.results.map(row => ({
        date: row.date,
        sales_volume: row.sales_volume || 0,
        production_volume: row.production_volume || 0,
        ratio: row.ratio || 0
      }));

    } catch (error) {
      console.error('计算每日产销比失败:', error);
      throw new DatabaseError('计算每日产销比失败', error);
    }
  }

  /**
   * 计算聚合产销比（总销量/总产量）
   */
  async calculateAggregateRatio(startDate: string, endDate: string): Promise<number> {
    try {
      const filterClause = ProductFilter.getCompleteFilter(true);

      const query = `
        SELECT 
          COALESCE(SUM(dm.sales_volume), 0) as total_sales,
          COALESCE(SUM(dm.production_volume), 0) as total_production
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ? AND ?
          AND (dm.sales_volume IS NOT NULL OR dm.production_volume IS NOT NULL)
          AND ${filterClause}
      `;

      const result = await this.db.queryFirst<{
        total_sales: number;
        total_production: number;
      }>(query, [startDate, endDate]);

      if (!result || result.total_production === 0) {
        return 0;
      }

      return Math.round((result.total_sales / result.total_production) * 100 * 100) / 100;

    } catch (error) {
      console.error('计算聚合产销比失败:', error);
      throw new DatabaseError('计算聚合产销比失败', error);
    }
  }

  /**
   * 计算产销比统计信息
   */
  async calculateStatistics(startDate: string, endDate: string): Promise<RatioStatistics> {
    try {
      const dailyRatios = await this.calculateDailyRatios(startDate, endDate);
      const aggregateRatio = await this.calculateAggregateRatio(startDate, endDate);

      if (dailyRatios.length === 0) {
        return {
          avg_ratio: 0,
          min_ratio: 0,
          max_ratio: 0,
          total_days: 0,
          total_sales: 0,
          total_production: 0,
          calculation_method: 'aggregate'
        };
      }

      const ratios = dailyRatios.map(d => d.ratio).filter(r => r > 0);
      const totalSales = dailyRatios.reduce((sum, d) => sum + d.sales_volume, 0);
      const totalProduction = dailyRatios.reduce((sum, d) => sum + d.production_volume, 0);

      return {
        avg_ratio: aggregateRatio, // 使用聚合方法计算的平均值
        min_ratio: ratios.length > 0 ? Math.min(...ratios) : 0,
        max_ratio: ratios.length > 0 ? Math.max(...ratios) : 0,
        total_days: dailyRatios.length,
        total_sales: totalSales,
        total_production: totalProduction,
        calculation_method: 'aggregate'
      };

    } catch (error) {
      console.error('计算产销比统计失败:', error);
      throw new DatabaseError('计算产销比统计失败', error);
    }
  }

  /**
   * 验证计算一致性
   */
  async validateConsistency(startDate: string, endDate: string): Promise<{
    isConsistent: boolean;
    aggregateRatio: number;
    dailyAverageRatio: number;
    difference: number;
    tolerance: number;
  }> {
    try {
      const aggregateRatio = await this.calculateAggregateRatio(startDate, endDate);
      const dailyRatios = await this.calculateDailyRatios(startDate, endDate);

      // 计算每日比率的平均值
      const validRatios = dailyRatios.map(d => d.ratio).filter(r => r > 0);
      const dailyAverageRatio = validRatios.length > 0 
        ? validRatios.reduce((sum, ratio) => sum + ratio, 0) / validRatios.length 
        : 0;

      const difference = Math.abs(aggregateRatio - dailyAverageRatio);
      const tolerance = 5.0; // 5%的容差
      const isConsistent = difference <= tolerance;

      return {
        isConsistent,
        aggregateRatio,
        dailyAverageRatio,
        difference,
        tolerance
      };

    } catch (error) {
      console.error('验证计算一致性失败:', error);
      throw new DatabaseError('验证计算一致性失败', error);
    }
  }

  /**
   * 获取生产数据一致性监控信息
   */
  async getProductionConsistency(): Promise<{
    total_records: number;
    records_with_production: number;
    records_with_sales: number;
    records_with_both: number;
    production_coverage: number;
    sales_coverage: number;
    data_quality_score: number;
  }> {
    try {
      const currentDate = new Date().toISOString().split('T')[0];
      const filterClause = ProductFilter.getCompleteFilter(true);

      const query = `
        SELECT 
          COUNT(*) as total_records,
          COUNT(CASE WHEN dm.production_volume IS NOT NULL AND dm.production_volume > 0 THEN 1 END) as records_with_production,
          COUNT(CASE WHEN dm.sales_volume IS NOT NULL AND dm.sales_volume > 0 THEN 1 END) as records_with_sales,
          COUNT(CASE WHEN dm.production_volume IS NOT NULL AND dm.production_volume > 0 
                     AND dm.sales_volume IS NOT NULL AND dm.sales_volume > 0 THEN 1 END) as records_with_both
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date = ?
          AND ${filterClause}
      `;

      const result = await this.db.queryFirst<{
        total_records: number;
        records_with_production: number;
        records_with_sales: number;
        records_with_both: number;
      }>(query, [currentDate]);

      if (!result || result.total_records === 0) {
        return {
          total_records: 0,
          records_with_production: 0,
          records_with_sales: 0,
          records_with_both: 0,
          production_coverage: 0,
          sales_coverage: 0,
          data_quality_score: 0
        };
      }

      const productionCoverage = (result.records_with_production / result.total_records) * 100;
      const salesCoverage = (result.records_with_sales / result.total_records) * 100;
      const dataQualityScore = (result.records_with_both / result.total_records) * 100;

      return {
        total_records: result.total_records,
        records_with_production: result.records_with_production,
        records_with_sales: result.records_with_sales,
        records_with_both: result.records_with_both,
        production_coverage: Math.round(productionCoverage * 100) / 100,
        sales_coverage: Math.round(salesCoverage * 100) / 100,
        data_quality_score: Math.round(dataQualityScore * 100) / 100
      };

    } catch (error) {
      console.error('获取生产数据一致性失败:', error);
      throw new DatabaseError('获取生产数据一致性失败', error);
    }
  }

  /**
   * 获取调试用的产销比数据
   */
  async getDebugRatioData(startDate: string, endDate: string): Promise<{
    daily_ratios: DailyRatio[];
    aggregate_metrics: AggregateMetrics;
    statistics: RatioStatistics;
    consistency_check: any;
  }> {
    try {
      const [dailyRatios, statistics, consistencyCheck] = await Promise.all([
        this.calculateDailyRatios(startDate, endDate),
        this.calculateStatistics(startDate, endDate),
        this.validateConsistency(startDate, endDate)
      ]);

      const aggregateMetrics: AggregateMetrics = {
        totalSales: statistics.total_sales,
        totalProduction: statistics.total_production,
        totalSalesAmount: 0, // 需要单独计算
        totalProducts: 0 // 需要单独计算
      };

      return {
        daily_ratios: dailyRatios,
        aggregate_metrics: aggregateMetrics,
        statistics,
        consistency_check: consistencyCheck
      };

    } catch (error) {
      console.error('获取调试产销比数据失败:', error);
      throw new DatabaseError('获取调试产销比数据失败', error);
    }
  }

  /**
   * 获取生产趋势数据
   */
  async getProductionTrends(startDate: string, endDate: string): Promise<{
    date: string;
    production_volume: number;
    sales_volume: number;
    ratio: number;
  }[]> {
    try {
      const dailyRatios = await this.calculateDailyRatios(startDate, endDate);
      
      return dailyRatios.map(ratio => ({
        date: ratio.date,
        production_volume: ratio.production_volume,
        sales_volume: ratio.sales_volume,
        ratio: ratio.ratio
      }));

    } catch (error) {
      console.error('获取生产趋势数据失败:', error);
      throw new DatabaseError('获取生产趋势数据失败', error);
    }
  }
}
