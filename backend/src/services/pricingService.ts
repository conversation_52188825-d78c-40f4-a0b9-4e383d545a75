/**
 * 价格业务服务
 * 处理价格监控相关的业务逻辑
 */

import type { D1Database } from '@cloudflare/workers-types';
import { DatabaseManager } from '../utils/database';
import { ProductFilter } from '../utils/filters';
import { DatabaseError } from '../middleware/errorHandler';
import type { 
  DailyDropRow, 
  ConsecutiveDropRow, 
  VolatilityRow,
  PriceTrendsSummary,
  PricingQuery 
} from '../types/api';

export class PricingService {
  private db: DatabaseManager;

  constructor(database: D1Database) {
    this.db = new DatabaseManager(database);
  }

  /**
   * 获取价格变动记录
   */
  async getPriceChanges(options: PricingQuery): Promise<{
    product_name: string;
    adjustment_date: string;
    previous_price: number;
    current_price: number;
    price_difference: number;
    change_percentage: number;
  }[]> {
    try {
      const { start_date, end_date, min_price_diff = '200' } = options;
      
      if (!start_date || !end_date) {
        throw new Error('start_date and end_date are required');
      }

      const filterClause = ProductFilter.getPriceAdjustmentFilter('pa');
      const minPriceDiff = parseFloat(min_price_diff);

      const query = `
        SELECT 
          pa.product_name,
          pa.adjustment_date,
          pa.previous_price,
          pa.current_price,
          pa.price_difference,
          ROUND((pa.price_difference / NULLIF(pa.previous_price, 0)) * 100, 2) as change_percentage
        FROM PriceAdjustments pa
        WHERE pa.adjustment_date BETWEEN ? AND ?
          AND ABS(pa.price_difference) >= ?
          AND ${filterClause}
        ORDER BY pa.adjustment_date DESC, ABS(pa.price_difference) DESC
      `;

      const result = await this.db.query<{
        product_name: string;
        adjustment_date: string;
        previous_price: number;
        current_price: number;
        price_difference: number;
        change_percentage: number;
      }>(query, [start_date, end_date, minPriceDiff]);

      return result.results;

    } catch (error) {
      console.error('获取价格变动记录失败:', error);
      throw new DatabaseError('获取价格变动记录失败', error);
    }
  }

  /**
   * 获取价格趋势数据
   */
  async getPriceTrends(options: PricingQuery): Promise<{
    date: string;
    price: number;
    change: number;
  }[]> {
    try {
      const { start_date, end_date, product_name } = options;
      
      if (!start_date || !end_date) {
        throw new Error('start_date and end_date are required');
      }

      let query = `
        SELECT 
          pa.adjustment_date as date,
          pa.current_price as price,
          pa.price_difference as change
        FROM PriceAdjustments pa
        WHERE pa.adjustment_date BETWEEN ? AND ?
      `;

      const params: any[] = [start_date, end_date];

      if (product_name) {
        query += ' AND pa.product_name = ?';
        params.push(product_name);
      }

      query += ' ORDER BY pa.adjustment_date ASC';

      const result = await this.db.query<{
        date: string;
        price: number;
        change: number;
      }>(query, params);

      return result.results;

    } catch (error) {
      console.error('获取价格趋势数据失败:', error);
      throw new DatabaseError('获取价格趋势数据失败', error);
    }
  }

  /**
   * 获取价格趋势汇总
   */
  async getPriceTrendsSummary(options: PricingQuery): Promise<PriceTrendsSummary> {
    try {
      const { start_date, end_date, product_id, product_name, limit = '100' } = options;
      
      if (!start_date || !end_date) {
        throw new Error('start_date and end_date are required');
      }

      let query = `
        SELECT 
          COUNT(DISTINCT pa.product_id) as total_products,
          MIN(pa.adjustment_date) as start_date,
          MAX(pa.adjustment_date) as end_date,
          AVG(pa.current_price) as avg_price,
          MIN(pa.current_price) as min_price,
          MAX(pa.current_price) as max_price,
          COUNT(*) as total_adjustments
        FROM PriceAdjustments pa
        WHERE pa.adjustment_date BETWEEN ? AND ?
      `;

      const params: any[] = [start_date, end_date];

      if (product_id) {
        query += ' AND pa.product_id = ?';
        params.push(parseInt(product_id));
      }

      if (product_name) {
        query += ' AND pa.product_name = ?';
        params.push(product_name);
      }

      const result = await this.db.queryFirst<PriceTrendsSummary>(query, params);

      return result || {
        total_products: 0,
        start_date: start_date,
        end_date: end_date,
        avg_price: 0,
        min_price: 0,
        max_price: 0,
        total_adjustments: 0
      };

    } catch (error) {
      console.error('获取价格趋势汇总失败:', error);
      throw new DatabaseError('获取价格趋势汇总失败', error);
    }
  }

  /**
   * 获取价格预警
   * 基于价格调整数据生成预警
   */
  async getPriceAlerts(options: PricingQuery): Promise<{
    id: number;
    product_name: string;
    alert_type: string;
    alert_level: string;
    alert_date: string;
    current_price: number;
    previous_price?: number;
    price_difference?: number;
    change_percentage?: number;
    alert_message: string;
    acknowledged: boolean;
  }[]> {
    try {
      const { alert_level, start_date, end_date, limit = '50' } = options;
      const limitNum = parseInt(limit);

      // 使用价格调整数据生成预警
      let query = `
        SELECT 
          pa.adjustment_id as id,
          pa.product_name,
          CASE 
            WHEN ABS(pa.price_difference) > 1000 THEN 'HIGH_VOLATILITY'
            WHEN pa.price_difference < -500 THEN 'PRICE_DROP'
            WHEN pa.price_difference > 500 THEN 'PRICE_RISE'
            ELSE 'NORMAL'
          END as alert_type,
          CASE 
            WHEN ABS(pa.price_difference) > 2000 THEN 'CRITICAL'
            WHEN ABS(pa.price_difference) > 1000 THEN 'HIGH'
            WHEN ABS(pa.price_difference) > 500 THEN 'MEDIUM'
            ELSE 'LOW'
          END as alert_level,
          pa.adjustment_date as alert_date,
          pa.current_price,
          pa.previous_price,
          pa.price_difference,
          ROUND((pa.price_difference / NULLIF(pa.previous_price, 0)) * 100, 2) as change_percentage,
          CASE 
            WHEN pa.price_difference < -500 THEN '价格大幅下跌，需要关注'
            WHEN pa.price_difference > 500 THEN '价格大幅上涨，需要关注'
            ELSE '价格正常波动'
          END as alert_message,
          0 as acknowledged
        FROM PriceAdjustments pa
        WHERE ABS(pa.price_difference) > 200
      `;

      const params: any[] = [];

      if (alert_level && alert_level !== 'ALL') {
        query += ` AND (
          CASE 
            WHEN ABS(pa.price_difference) > 2000 THEN 'CRITICAL'
            WHEN ABS(pa.price_difference) > 1000 THEN 'HIGH'
            WHEN ABS(pa.price_difference) > 500 THEN 'MEDIUM'
            ELSE 'LOW'
          END = ?
        )`;
        params.push(alert_level);
      }

      if (start_date && end_date) {
        query += ' AND pa.adjustment_date BETWEEN ? AND ?';
        params.push(start_date, end_date);
      }

      query += ' ORDER BY ABS(pa.price_difference) DESC LIMIT ?';
      params.push(limitNum);

      const result = await this.db.query<{
        id: number;
        product_name: string;
        alert_type: string;
        alert_level: string;
        alert_date: string;
        current_price: number;
        previous_price: number;
        price_difference: number;
        change_percentage: number;
        alert_message: string;
        acknowledged: number;
      }>(query, params);

      return result.results.map(row => ({
        ...row,
        acknowledged: row.acknowledged === 1
      }));

    } catch (error) {
      console.error('获取价格预警失败:', error);
      throw new DatabaseError('获取价格预警失败', error);
    }
  }

  /**
   * 获取价格变动排名
   */
  async getPriceRankings(options: {
    period?: string;
    start_date?: string;
    end_date?: string;
    ranking_type?: string;
    limit?: string;
  }): Promise<{
    rank: number;
    product_name: string;
    price_change: number;
    change_percentage: number;
    current_price: number;
    previous_price: number;
  }[]> {
    try {
      const { period = 'daily', start_date, end_date, ranking_type = 'drop_amount', limit = '20' } = options;

      if (!start_date || !end_date) {
        throw new Error('start_date and end_date are required');
      }

      const filterClause = ProductFilter.getPriceAdjustmentFilter('pa');
      let orderBy = 'pa.price_difference ASC'; // 默认按降幅排序

      if (ranking_type === 'drop_percentage') {
        orderBy = '(pa.price_difference / NULLIF(pa.previous_price, 0)) ASC';
      } else if (ranking_type === 'rise_amount') {
        orderBy = 'pa.price_difference DESC';
      } else if (ranking_type === 'rise_percentage') {
        orderBy = '(pa.price_difference / NULLIF(pa.previous_price, 0)) DESC';
      }

      const query = `
        SELECT 
          pa.product_name,
          pa.price_difference as price_change,
          ROUND((pa.price_difference / NULLIF(pa.previous_price, 0)) * 100, 2) as change_percentage,
          pa.current_price,
          pa.previous_price
        FROM PriceAdjustments pa
        WHERE pa.adjustment_date BETWEEN ? AND ?
          AND ${filterClause}
        ORDER BY ${orderBy}
        LIMIT ?
      `;

      const result = await this.db.query<{
        product_name: string;
        price_change: number;
        change_percentage: number;
        current_price: number;
        previous_price: number;
      }>(query, [start_date, end_date, parseInt(limit)]);

      return result.results.map((item, index) => ({
        rank: index + 1,
        ...item
      }));

    } catch (error) {
      console.error('获取价格变动排名失败:', error);
      throw new DatabaseError('获取价格变动排名失败', error);
    }
  }

  /**
   * 确认价格预警
   * 支持两种模式：
   * 1. 基于PriceAdjustments的轻量级模式（当前）
   * 2. 基于PriceAlerts表的完整模式（需要执行price-alerts-schema.sql）
   */
  async acknowledgeAlert(alertId: string): Promise<boolean> {
    try {
      // 检查是否存在PriceAlerts表
      const tableCheckQuery = `
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='PriceAlerts'
      `;
      
      const tableExists = await this.db.queryFirst<{ name: string }>(tableCheckQuery);
      
      if (tableExists) {
        // 完整模式：更新PriceAlerts表
        const updateQuery = `
          UPDATE PriceAlerts 
          SET acknowledged = TRUE, acknowledged_at = datetime('now')
          WHERE alert_id = ?
        `;
        
        const result = await this.db.query(updateQuery, [parseInt(alertId)]);
        console.log(`预警ID ${alertId} 已确认`);
        return result.success;
        
      } else {
        // 轻量级模式：基于PriceAdjustments的模拟确认
        // 实际项目中可以考虑创建一个简单的确认记录表
        console.log(`轻量级模式：预警ID ${alertId} 确认成功（基于PriceAdjustments数据）`);
        return true;
      }

    } catch (error) {
      console.error('确认价格预警失败:', error);
      throw new DatabaseError('确认价格预警失败', error);
    }
  }
}
