import { Hono } from 'hono';
import { cors } from 'hono/cors';
import type { D1Database } from '@cloudflare/workers-types';
import * as XLSX from 'xlsx';

// Define interfaces for database query results to ensure type safety
interface DailyDropRow {
  product_id: number;
  product_name: string;
  current_price: number;
  previous_price: number;
  price_difference: number;
  adjustment_date: string;
  change_percentage: number;
}

interface ConsecutiveDropRow {
  adjustment_date: string;
  current_price: number;
  previous_price: number;
  price_difference: number;
  product_name: string;
}

interface VolatilityRow {
  current_price: number;
  adjustment_date: string;
  product_name: string;
}

interface ProductIdRow {
  product_id: number;
}

interface PriceTrendsSummary {
  total_products: number;
  start_date: string;
  end_date: string;
  avg_price: number;
  min_price: number;
  max_price: number;
  total_adjustments: number;
}
// Comprehensive CORS configuration
const ALLOWED_ORIGINS = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3003',
  'http://localhost:3004',
  'http://127.0.0.1:3000',
  'http://localhost:8000',
  'http://localhost:8080',
  'https://my-fullstack-project.pages.dev',
  'https://7bbead58.my-fullstack-project.pages.dev',
  'https://backend.qu18354531302.workers.dev',
  'https://my-auth-worker.qu18354531302.workers.dev'
];

// CORS headers for manual handling
const CORS_HEADERS = {
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Accept, Authorization, X-Requested-With',
  'Access-Control-Expose-Headers': 'Content-Length, X-Requested-With',
  'Access-Control-Max-Age': '86400',
};

// Define the bindings for Cloudflare environment variables
export type Bindings = {
  DB: D1Database;

/**
 * Define interfaces for database query results to ensure type safety
 */
};

const app = new Hono<{ Bindings: Bindings }>();

// --- Business Logic Layer ---

/**
 * Interfaces for production ratio calculations
 */
interface DailyRatio {
  date: string;
  sales_volume: number;
  production_volume: number;
  ratio: number;
}

interface RatioStatistics {
  avg_ratio: number;
  min_ratio: number;
  max_ratio: number;
  total_days: number;
  total_sales: number;
  total_production: number;
  calculation_method: string;
}

interface AggregateMetrics {
  totalSales: number;
  totalProduction: number;
  totalSalesAmount: number;
  totalProducts: number;
}

/**
 * Centralized Production Ratio Calculator
 * This class provides the authoritative calculation logic for all production ratio metrics
 * ensuring consistency across all API endpoints and UI components
 */
class ProductionRatioCalculator {
  private db: D1Database;

  constructor(db: D1Database) {
    this.db = db;
  }

  /**
   * Calculate aggregate metrics for production and sales data
   * Used by dashboard summary and ratio statistics
   */
  async calculateAggregateMetrics(startDate: string, endDate: string): Promise<AggregateMetrics> {
    try {
      // Use sales filter for sales data, inventory filter for production
      const salesFilterClause = ProductFilter.getCompleteFilter(true, 'p');
      const inventoryFilterClause = ProductFilter.getCompleteFilter(false, 'p');

      const summaryQuery = `
        SELECT
          (
            SELECT SUM(dm.sales_volume) 
            FROM DailyMetrics dm 
            JOIN Products p ON dm.product_id = p.product_id 
            WHERE dm.record_date BETWEEN ?1 AND ?2 
              AND dm.sales_volume IS NOT NULL 
              AND ${salesFilterClause}
          ) as total_sales_volume,
          (
            SELECT SUM(dm.production_volume) 
            FROM DailyMetrics dm 
            JOIN Products p ON dm.product_id = p.product_id 
            WHERE dm.record_date BETWEEN ?1 AND ?2 
              AND dm.production_volume IS NOT NULL 
              AND ${inventoryFilterClause}
          ) as total_production_volume,
          (
            SELECT SUM(dm.sales_amount) 
            FROM DailyMetrics dm 
            JOIN Products p ON dm.product_id = p.product_id 
            WHERE dm.record_date BETWEEN ?1 AND ?2 
              AND dm.sales_amount IS NOT NULL 
              AND ${salesFilterClause}
          ) as total_sales_amount,
          (
            SELECT COUNT(DISTINCT p.product_id) 
            FROM DailyMetrics dm 
            JOIN Products p ON dm.product_id = p.product_id 
            WHERE dm.record_date BETWEEN ?1 AND ?2 
              AND ${salesFilterClause}
          ) as total_products
      `;

      const summaryPs = this.db.prepare(summaryQuery).bind(startDate, endDate);
      const summaryResult = await summaryPs.first<{
        total_sales_volume: number;
        total_production_volume: number;
        total_sales_amount: number;
        total_products: number;
      }>();

      return {
        totalSales: summaryResult?.total_sales_volume || 0,
        totalProduction: summaryResult?.total_production_volume || 0,
        totalSalesAmount: summaryResult?.total_sales_amount || 0,
        totalProducts: summaryResult?.total_products || 0
      };
    } catch (error) {
      console.error('Error in calculateAggregateMetrics:', error);
      throw new Error(`Failed to calculate aggregate metrics: ${error}`);
    }
  }

  /**
   * Calculate authoritative average production ratio using aggregate totals method
   * This is the single source of truth for average ratio calculations
   */
  async calculateAggregateRatio(startDate: string, endDate: string): Promise<number> {
    try {
      const metrics = await this.calculateAggregateMetrics(startDate, endDate);
      
      let avgRatio = 0;
      let calculationMethod = '';
      
      if (metrics.totalProduction === 0) {
        if (metrics.totalSales > 0) {
          avgRatio = -1; // N/A indicator
          calculationMethod = 'Zero production with sales (N/A)';
        } else {
          avgRatio = 0;
          calculationMethod = 'Zero production and zero sales';
        }
      } else {
        avgRatio = (metrics.totalSales / metrics.totalProduction) * 100;
        calculationMethod = 'Aggregate method: (Total Sales / Total Production) × 100';
        
        if (avgRatio > 500) {
          console.log(`⚠️ [Calculator] Ratio clipping applied: ${avgRatio.toFixed(2)}% -> 500.00%`);
          avgRatio = 500; // Apply clipping logic
          calculationMethod += ' (clipped at 500%)';
        }
      }

      // Enhanced logging with traceability information
      console.log(`📊 [Calculator] Aggregate ratio calculated: ${avgRatio.toFixed(2)}%`);
      console.log(`📋 [Calculator] Calculation details:
        - Date range: ${startDate} to ${endDate}
        - Total sales: ${metrics.totalSales.toFixed(2)}
        - Total production: ${metrics.totalProduction.toFixed(2)}
        - Method: ${calculationMethod}
        - Result: ${avgRatio.toFixed(2)}%
      `);
      
      return avgRatio;
    } catch (error) {
      console.error('❌ Error calculating aggregate ratio:', error);
      throw new Error(`Failed to calculate aggregate ratio: ${error}`);
    }
  }

  /**
   * Calculate daily production ratios with consistent filtering
   */
  async calculateDailyRatios(startDate: string, endDate: string): Promise<DailyRatio[]> {
    try {
      console.log(`📅 [Calculator] Calculating daily ratios for ${startDate} to ${endDate}`);
      
      // Use different filters for sales vs production data
      const salesFilterClause = ProductFilter.getCompleteFilter(true, 'p');
      const productionFilterClause = ProductFilter.getCompleteFilter(false, 'p');
      console.log(`🔍 [Calculator] Using sales filter: ${salesFilterClause}`);
      console.log(`🔍 [Calculator] Using production filter: ${productionFilterClause}`);

      // Get sales data (includes fresh products)
      const salesQuery = `
        SELECT
          DATE(dm.record_date) as record_date,
          SUM(dm.sales_volume) as daily_sales_volume
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ?1 AND ?2
          AND dm.sales_volume IS NOT NULL
          AND ${salesFilterClause}
        GROUP BY DATE(dm.record_date)
        ORDER BY record_date ASC
      `;

      // Get production data
      // Get production data (excludes fresh products)
      const productionQuery = `
        SELECT
          DATE(dm.record_date) as record_date,
          SUM(dm.production_volume) as daily_production_volume
        FROM DailyMetrics dm
        JOIN Products p ON dm.product_id = p.product_id
        WHERE dm.record_date BETWEEN ?1 AND ?2
          AND dm.production_volume IS NOT NULL
          AND dm.production_volume > 0
          AND ${productionFilterClause}
        GROUP BY DATE(dm.record_date)
        ORDER BY record_date ASC
      `;

      const salesPs = this.db.prepare(salesQuery).bind(startDate, endDate);
      const productionPs = this.db.prepare(productionQuery).bind(startDate, endDate);

      const { results: salesResults } = await salesPs.all();
      const { results: productionResults } = await productionPs.all();

      console.log(`📊 [Calculator] Retrieved sales data for ${salesResults.length} days`);
      console.log(`📊 [Calculator] Retrieved production data for ${productionResults.length} days`);

      // Combine sales and production data
      const salesMap = new Map();
      const productionMap = new Map();

      salesResults.forEach((row: any) => {
        salesMap.set(row.record_date, row.daily_sales_volume || 0);
      });

      productionResults.forEach((row: any) => {
        productionMap.set(row.record_date, row.daily_production_volume || 0);
      });

      // Calculate daily ratios
      const allDates = new Set([...salesMap.keys(), ...productionMap.keys()]);
      const dailyRatios: DailyRatio[] = [];
      let clippedDays = 0;
      let zeroProdDays = 0;

      for (const date of Array.from(allDates).sort()) {
        const salesVol = salesMap.get(date) || 0;
        const prodVol = productionMap.get(date) || 0;
        
        let ratio = 0;
        let calculationNote = '';
        
        if (prodVol > 0) {
          ratio = (salesVol / prodVol) * 100;
          
          if (ratio > 500) {
            calculationNote = `Clipped from ${ratio.toFixed(2)}% to 500.00%`;
            ratio = 500; // Apply clipping logic
            clippedDays++;
          }
        } else {
          calculationNote = 'Zero production day';
          zeroProdDays++;
        }

        dailyRatios.push({
          date: date,
          sales_volume: salesVol,
          production_volume: prodVol,
          ratio: ratio
        });
        
        // Log any special cases for traceability
        if (calculationNote) {
          console.log(`📝 [Calculator] ${date}: ${calculationNote} (Sales: ${salesVol}, Production: ${prodVol})`);
        }
      }

      console.log(`📊 [Calculator] Daily ratios calculation summary:
        - Total days: ${dailyRatios.length}
        - Days with clipped ratios (>500%): ${clippedDays}
        - Days with zero production: ${zeroProdDays}
      `);
      
      return dailyRatios;
    } catch (error) {
      console.error('❌ Error calculating daily ratios:', error);
      throw new Error(`Failed to calculate daily ratios: ${error}`);
    }
  }

  /**
   * Calculate comprehensive ratio statistics with consistency validation
   */
  async calculateStatistics(startDate: string, endDate: string): Promise<RatioStatistics> {
    try {
      // Get authoritative average using aggregate method
      const avgRatio = await this.calculateAggregateRatio(startDate, endDate);
      const metrics = await this.calculateAggregateMetrics(startDate, endDate);
      
      // Get daily ratios for min/max calculation
      const dailyRatios = await this.calculateDailyRatios(startDate, endDate);
      const ratioValues = dailyRatios.map(d => d.ratio).filter(r => r > 0);

      const minRatio = ratioValues.length > 0 ? Math.min(...ratioValues) : 0;
      const maxRatio = ratioValues.length > 0 ? Math.max(...ratioValues) : 0;

      const statistics: RatioStatistics = {
        avg_ratio: avgRatio,
        min_ratio: minRatio,
        max_ratio: maxRatio,
        total_days: dailyRatios.length,
        total_sales: metrics.totalSales / 1000, // 转换为吨，保持单位一致性
        total_production: metrics.totalProduction / 1000, // 转换为吨，保持单位一致性
        calculation_method: 'Unified Aggregate Method'
      };

      console.log(`🔍 [Calculator] Statistics calculated:`, statistics);
      return statistics;
    } catch (error) {
      console.error('Error calculating statistics:', error);
      throw new Error(`Failed to calculate statistics: ${error}`);
    }
  }

  /**
   * Validate consistency between different calculation methods
   * Used for debugging and monitoring
   */
  async validateConsistency(startDate: string, endDate: string): Promise<{
    isConsistent: boolean;
    aggregateRatio: number;
    dailyAverageRatio: number;
    difference: number;
    details: {
      dateRange: { start: string, end: string };
      totalSales: number;
      totalProduction: number;
      dailyRatiosCount: number;
      validDailyRatiosCount: number;
      calculationTimestamp: string;
    };
  }> {
    try {
      console.log(`🔍 [Calculator] Validating data consistency for ${startDate} to ${endDate}`);
      
      // Get metrics for detailed reporting
      const metrics = await this.calculateAggregateMetrics(startDate, endDate);
      const aggregateRatio = await this.calculateAggregateRatio(startDate, endDate);
      const dailyRatios = await this.calculateDailyRatios(startDate, endDate);
      
      // Calculate daily average method for comparison
      const validRatios = dailyRatios.filter(d => d.ratio > 0).map(d => d.ratio);
      const dailyAverageRatio = validRatios.length > 0 
        ? validRatios.reduce((sum, ratio) => sum + ratio, 0) / validRatios.length 
        : 0;

      const difference = Math.abs(aggregateRatio - dailyAverageRatio);
      const isConsistent = difference < 5.0; // Allow reasonable business variance (up to 5%)

      // Create detailed result object with additional information for monitoring
      const result = {
        isConsistent,
        aggregateRatio,
        dailyAverageRatio,
        difference,
        details: {
          dateRange: { start: startDate, end: endDate },
          totalSales: metrics.totalSales,
          totalProduction: metrics.totalProduction,
          dailyRatiosCount: dailyRatios.length,
          validDailyRatiosCount: validRatios.length,
          calculationTimestamp: new Date().toISOString()
        }
      };

      if (!isConsistent) {
        console.warn(`⚠️ [Calculator] Consistency check failed:`);
        console.warn(`⚠️ [Calculator] Aggregate ratio: ${aggregateRatio.toFixed(2)}%`);
        console.warn(`⚠️ [Calculator] Daily average ratio: ${dailyAverageRatio.toFixed(2)}%`);
        console.warn(`⚠️ [Calculator] Difference: ${difference.toFixed(2)}%`);
        console.warn(`⚠️ [Calculator] Date range: ${startDate} to ${endDate}`);
        console.warn(`⚠️ [Calculator] This inconsistency may indicate a calculation error or data issue`);
        
        // Log detailed information for debugging
        console.log(`📊 [Calculator] Detailed metrics for debugging:
          - Total sales: ${metrics.totalSales.toFixed(2)}
          - Total production: ${metrics.totalProduction.toFixed(2)}
          - Total days: ${dailyRatios.length}
          - Days with valid ratios: ${validRatios.length}
          - Aggregate calculation: (${metrics.totalSales.toFixed(2)} / ${metrics.totalProduction.toFixed(2)}) * 100 = ${aggregateRatio.toFixed(2)}%
          - Daily average calculation: Sum of daily ratios / valid days = ${dailyAverageRatio.toFixed(2)}%
        `);
      } else {
        console.log(`✅ [Calculator] Consistency check passed: difference ${difference.toFixed(4)}%`);
      }

      return result;
    } catch (error) {
      console.error('❌ Error validating consistency:', error);
      throw new Error(`Failed to validate consistency: ${error}`);
    }
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use ProductionRatioCalculator instead
 */
async function calculateAggregateMetrics(
  db: D1Database,
  startDate: string,
  endDate: string
): Promise<AggregateMetrics> {
  const calculator = new ProductionRatioCalculator(db);
  return calculator.calculateAggregateMetrics(startDate, endDate);
}

/**
 * Centralized product filtering logic based on original Python script requirements
 * Filters out:
 * 1. Fresh products (starting with '鲜') except '凤肠' products
 * 2. By-products and empty categories (when category filtering is enabled)
 */
class ProductFilter {
  /**
   * Generate SQL WHERE clause for inventory filtering (strict - excludes fresh products)
   * Used for inventory data only
   */
  static getInventoryFilter(tableAlias: string = 'p'): string {
    return `(
      -- Exclude fresh products except feng chang
      (${tableAlias}.product_name NOT LIKE '%鲜%' OR ${tableAlias}.product_name LIKE '%凤肠%')
      AND
      -- Exclude by-products
      (${tableAlias}.category IS NULL OR ${tableAlias}.category != '副产品')
    )`;
  }

  /**
   * Generate SQL WHERE clause for sales filtering (relaxed - includes fresh products)
   * Used for sales data and price trends
   * Note: Data is already filtered during import process, so no additional filtering needed
   */
  static getSalesFilter(tableAlias: string = 'p'): string {
    return `(
      -- No filtering needed - data already filtered during import with proper logic
      -- to include fresh products but exclude by-products and blanks
      1 = 1
    )`;
  }

  /**
   * Generate complete product filtering WHERE clause
   * @param forSales - If true, uses relaxed filtering for sales data
   * @param tableAlias - Table alias to use in the query
   */
  static getCompleteFilter(forSales: boolean = true, tableAlias: string = 'p'): string {
    if (forSales) {
      return this.getSalesFilter(tableAlias);
    } else {
      return this.getInventoryFilter(tableAlias);
    }
  }

  // Legacy methods for backward compatibility
  static getProductNameFilter(): string {
    // Legacy method - now delegates to inventory filter
    return this.getInventoryFilter('p');
  }

  static getCategoryFilter(strict: boolean = false): string {
    // Legacy method - now delegates to sales filter
    return this.getSalesFilter('p');
  }

  /**
   * Generate filtering for PriceAdjustments table (uses pa.product_name and pa.category)
   */
  static getPriceAdjustmentFilter(strict: boolean = false): string {
    const productNameFilter = `(
      pa.product_name NOT LIKE '鲜%'
      OR
      pa.product_name LIKE '%凤肠%'
    )`;

    const categoryFilter = strict
      ? `(
          pa.category IS NOT NULL
          AND pa.category != ''
          AND pa.category NOT IN ('副产品', '生鲜品其他')
        )`
      : `(
          pa.category IS NULL
          OR pa.category = ''
          OR pa.category NOT IN ('副产品', '生鲜品其他')
        )`;

    return `${productNameFilter} AND ${categoryFilter}`;
  }
}

// Comprehensive CORS middleware that handles both preflight and actual requests
app.use('/*', async (c, next) => {
  const origin = c.req.header('Origin');
  const requestMethod = c.req.method;
  console.log(`Request URL: ${c.req.url}`); // Add this line to log the request URL

  // Determine if origin is allowed
  const isAllowedOrigin = origin && ALLOWED_ORIGINS.includes(origin);
  const allowOrigin = isAllowedOrigin ? origin : ALLOWED_ORIGINS[0]; // fallback to first allowed origin

  // Handle preflight OPTIONS requests
  if (requestMethod === 'OPTIONS') {
    return c.text('', 200, {
      'Access-Control-Allow-Origin': allowOrigin,
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Accept, Authorization, X-Requested-With',
      'Access-Control-Expose-Headers': 'Content-Length, X-Requested-With',
      'Access-Control-Max-Age': '86400',
      'Vary': 'Origin'
    });
  }

  // Process the actual request
  await next();

  // Add CORS headers to all responses
  c.res.headers.set('Access-Control-Allow-Origin', allowOrigin);
  c.res.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  c.res.headers.set('Access-Control-Allow-Headers', 'Content-Type, Accept, Authorization, X-Requested-With');
  c.res.headers.set('Access-Control-Expose-Headers', 'Content-Length, X-Requested-With');
  c.res.headers.set('Access-Control-Max-Age', '86400');
  c.res.headers.set('Vary', 'Origin');
});

// --- API Endpoints ---

// Health check endpoint
app.get('/health', async (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    endpoints: {
      inventory_total_summary: '/api/inventory/total-summary',
      dashboard_summary: '/api/dashboard/summary',
      system_date_range: '/api/system/date-range'
    }
  });
});

// System endpoint to get available date range from database
app.get('/api/system/date-range', async (c) => {
  try {
    console.log('🔄 Fetching available date range from database...');
    
    // Query to get the actual date range available in the database
    const dateRangeQuery = `
      SELECT 
        MIN(record_date) as start_date,
        MAX(record_date) as end_date,
        COUNT(DISTINCT record_date) as total_days
      FROM DailyMetrics 
      WHERE record_date IS NOT NULL
    `;
    
    const ps = c.env.DB.prepare(dateRangeQuery);
    const result = await ps.first<{
      start_date: string;
      end_date: string;
      total_days: number;
    }>();
    
    if (!result || !result.start_date || !result.end_date) {
      // Fallback to default range if no data found
      console.warn('⚠️ No date range found in database, using fallback values');
      return c.json({
        start_date: '2025-06-01',
        end_date: '2025-06-26',
        total_days: 26,
        source: 'fallback',
        message: 'No data found in database, using default range'
      });
    }
    
    const response = {
      start_date: result.start_date,
      end_date: result.end_date,
      total_days: result.total_days,
      source: 'database',
      last_updated: new Date().toISOString()
    };
    
    console.log('✅ Available date range loaded from database:', response);
    return c.json(response);
  } catch (e: any) {
    console.error('❌ Error fetching date range:', e);
    
    // Return fallback values on error
    return c.json({
      start_date: '2025-06-01',
      end_date: '2025-06-26',
      total_days: 26,
      source: 'fallback',
      error: e.message,
      message: 'Error occurred, using fallback date range'
    }, 200); // Return 200 with fallback data instead of error
  }
});


// Monitoring endpoint for production data consistency
app.get('/api/monitoring/production-consistency', async (c) => {
  try {
    // Get the current date
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // Format dates as YYYY-MM-DD
    const formatDate = (date: Date) => {
      return date.toISOString().split('T')[0];
    };
    
    // Check consistency for different time periods
    const calculator = new ProductionRatioCalculator(c.env.DB);
    
    // Last day
    const lastDayStart = formatDate(yesterday);
    const lastDayEnd = formatDate(yesterday);
    
    // Last 7 days
    const last7DaysStart = new Date(today);
    last7DaysStart.setDate(today.getDate() - 7);
    const last7DaysEnd = formatDate(yesterday);
    
    // Last 30 days
    const last30DaysStart = new Date(today);
    last30DaysStart.setDate(today.getDate() - 30);
    const last30DaysEnd = formatDate(yesterday);
    
    // Run all consistency checks in parallel
    const [lastDayResult, last7DaysResult, last30DaysResult] = await Promise.all([
      calculator.validateConsistency(lastDayStart, lastDayEnd).catch(e => ({
        isConsistent: false,
        error: e.message,
        period: 'last_day'
      })),
      calculator.validateConsistency(formatDate(last7DaysStart), last7DaysEnd).catch(e => ({
        isConsistent: false,
        error: e.message,
        period: 'last_7_days'
      })),
      calculator.validateConsistency(formatDate(last30DaysStart), last30DaysEnd).catch(e => ({
        isConsistent: false,
        error: e.message,
        period: 'last_30_days'
      }))
    ]);
    
    // Determine overall status
    const allConsistent = lastDayResult.isConsistent && 
                          last7DaysResult.isConsistent && 
                          last30DaysResult.isConsistent;
    
    // Create monitoring response with health status
    const monitoringResponse = {
      status: allConsistent ? 'healthy' : 'warning',
      timestamp: new Date().toISOString(),
      checks: {
        last_day: {
          period: `${lastDayStart} to ${lastDayEnd}`,
          status: lastDayResult.isConsistent ? 'pass' : 'fail',
          details: lastDayResult
        },
        last_7_days: {
          period: `${formatDate(last7DaysStart)} to ${last7DaysEnd}`,
          status: last7DaysResult.isConsistent ? 'pass' : 'fail',
          details: last7DaysResult
        },
        last_30_days: {
          period: `${formatDate(last30DaysStart)} to ${last30DaysEnd}`,
          status: last30DaysResult.isConsistent ? 'pass' : 'fail',
          details: last30DaysResult
        }
      },
      recommendations: allConsistent ? 
        [] : 
        ['Run data consistency validation tests', 'Check calculation methods in ProductionRatioCalculator']
    };
    
    // Return appropriate HTTP status code based on consistency
    return c.json(monitoringResponse, allConsistent ? 200 : 207);
  } catch (e: any) {
    console.error('Error in monitoring endpoint:', e);
    return c.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: e.message,
      recommendations: ['Check server logs for details', 'Verify database connectivity']
    }, 500);
  }
});

app.post('/api/register', async (c) => {
  const { username, password, inviteCode } = await c.req.json();

  if (!username || !password || !inviteCode) {
    return c.json({ success: false, message: 'Missing required fields' }, 400);
  }

  // In a real application, you would have a database of invite codes.
  if (inviteCode !== 'SPRING2025') {
    return c.json({ success: false, message: 'Invalid invite code' }, 400);
  }

  try {
    // In a real application, you would hash the password before storing it.
    const ps = c.env.DB.prepare('INSERT INTO Users (username, password) VALUES (?, ?)').bind(username, password);
    await ps.run();

    // In a real application, you would generate a proper JWT.
    const token = 'mock-token-' + Date.now();
    const user = { username, avatar: username.charAt(0).toUpperCase() };

    return c.json({ success: true, token, user });
  } catch (e: any) {
    if (e.message.includes('UNIQUE constraint failed')) {
      return c.json({ success: false, message: 'Username already exists' }, 409);
    }
    return c.json({ success: false, message: 'Registration failed', details: e.message }, 500);
  }
});

app.post('/api/login', async (c) => {
  const { username, password } = await c.req.json();

  if (!username || !password) {
    return c.json({ success: false, message: 'Missing required fields' }, 400);
  }

  try {
    const ps = c.env.DB.prepare('SELECT * FROM Users WHERE username = ? AND password = ?').bind(username, password);
    const user = await ps.first<{ username: string }>();

    if (user) {
      // In a real application, you would generate a proper JWT.
      const token = 'mock-token-' + Date.now();
      const userResponse = { username: user.username, avatar: user.username.charAt(0).toUpperCase() };
      return c.json({ success: true, token, user: userResponse });
    } else {
      return c.json({ success: false, message: 'Invalid username or password' }, 401);
    }
  } catch (e: any) {
    return c.json({ success: false, message: 'Login failed', details: e.message }, 500);
  }
});

// Endpoint to get all products
app.get('/api/products', async (c) => {
  try {
    const ps = c.env.DB.prepare('SELECT * FROM Products ORDER BY product_name');
    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for the summary cards
// Uses Python script equivalent logic for accurate ratio calculation
app.get('/api/dashboard/summary', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    // Calculate days between dates
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    const timeDiff = endDate.getTime() - startDate.getTime();
    const days = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;

    // Use the shared aggregate metrics function
    const aggregateMetrics = await calculateAggregateMetrics(c.env.DB, start_date, end_date);

    // Get total inventory for the end date (remains separate as it's not part of aggregate metrics)
    const filterClause = ProductFilter.getCompleteFilter(false, 'p');
    const inventoryQuery = `
      SELECT SUM(dm.inventory_level) as total_inventory
      FROM DailyMetrics dm
      JOIN Products p ON dm.product_id = p.product_id
      WHERE dm.record_date = ?1
        AND dm.inventory_level IS NOT NULL
        AND dm.inventory_level > 0
        AND ${filterClause}
    `;

    const inventoryPs = c.env.DB.prepare(inventoryQuery).bind(end_date);
    const inventoryResult = await inventoryPs.first<{ total_inventory: number }>();

    const totalSales = aggregateMetrics.totalSales;
    const totalProduction = aggregateMetrics.totalProduction;
    const totalSalesAmount = aggregateMetrics.totalSalesAmount;
    const totalProducts = aggregateMetrics.totalProducts;
    const totalInventory = inventoryResult?.total_inventory || 0;

    // Calculate metrics with improved logic
    const averagePrice = totalSales > 0 ? (totalSalesAmount / totalSales) * 1000.0 : 0;
    let salesToProductionRatio = 0;
    if (totalProduction === 0) {
      if (totalSales > 0) {
        salesToProductionRatio = -1; // N/A
      } else {
        salesToProductionRatio = 0;
      }
    } else {
      salesToProductionRatio = (totalSales / totalProduction) * 100;
      salesToProductionRatio = Math.min(salesToProductionRatio, 500); // Clipping
    }

    return c.json({
      total_products: totalProducts,
      days: days,
      total_sales: totalSales / 1000, // 转换为吨，保持单位一致性
      total_production: totalProduction / 1000, // 转换为吨，保持单位一致性
      total_sales_amount: totalSalesAmount, // Added for clarity
      total_inventory: totalInventory / 1000, // 转换为吨
      average_price: averagePrice,
      sales_to_production_ratio: salesToProductionRatio,
      production_ratio: salesToProductionRatio, // 保持兼容性
      calculation_method: 'Consolidated SQL Query'
    });
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for the top N inventory products chart
// Implements data filtering logic from original Python script
app.get('/api/inventory/top', async (c) => {
  const { date, limit = '15', end_date } = c.req.query();
  let targetDate = end_date || date; // Prioritize end_date for range selection

  if (!targetDate) {
    return c.json({ error: 'Missing date or end_date query parameter' }, 400);
  }

  try {
    // Use centralized filtering logic (for inventory data - excludes fresh products)
    const filterClause = ProductFilter.getCompleteFilter(false);

    // First get total inventory for percentage calculation, based on the target date
    let totalPs = c.env.DB.prepare(
      `SELECT SUM(dm.inventory_level) as total_inventory
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.inventory_level IS NOT NULL
         AND dm.inventory_level > 0
         AND ${filterClause}`
    ).bind(targetDate);

    let totalResult = await totalPs.first<{ total_inventory: number }>();
    let totalInventory = totalResult?.total_inventory || 0;

    // Smart date detection: if no data for specified date, find latest available date
    if (totalInventory === 0) {
      console.log(`No inventory data found for ${targetDate}, searching for latest available date...`);
      
      const latestDatePs = c.env.DB.prepare(
        `SELECT record_date
         FROM DailyMetrics dm
         JOIN Products p ON dm.product_id = p.product_id
         WHERE dm.inventory_level IS NOT NULL 
           AND dm.inventory_level > 0
           AND ${filterClause}
         ORDER BY record_date DESC
         LIMIT 1`
      );
      
      const latestDateResult = await latestDatePs.first<{ record_date: string }>();
      
      if (latestDateResult?.record_date) {
        targetDate = latestDateResult.record_date;
        console.log(`Found latest inventory date: ${targetDate}`);
        
        // Recalculate total inventory with the latest date
        totalPs = c.env.DB.prepare(
          `SELECT SUM(dm.inventory_level) as total_inventory
           FROM DailyMetrics dm
           JOIN Products p ON dm.product_id = p.product_id
           WHERE dm.record_date = ?1
             AND dm.inventory_level IS NOT NULL
             AND dm.inventory_level > 0
             AND ${filterClause}`
        ).bind(targetDate);

        totalResult = await totalPs.first<{ total_inventory: number }>();
        totalInventory = totalResult?.total_inventory || 0;
      }
    }

    // Get the top N products based on the target date (potentially updated)
    const ps = c.env.DB.prepare(
      `SELECT
         p.product_name,
         dm.inventory_level / 1000.0 as inventory_level,
         (CASE WHEN ?3 > 0 THEN ROUND((dm.inventory_level * 100.0 / ?3), 2) ELSE 0 END) as percentage,
         ROW_NUMBER() OVER (ORDER BY dm.inventory_level DESC) as rank
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.inventory_level IS NOT NULL
         AND dm.inventory_level > 0
         AND ${filterClause}
       ORDER BY dm.inventory_level DESC
       LIMIT ?2`
    ).bind(targetDate, parseInt(limit, 10), totalInventory);

    const { results } = await ps.all();
    console.log(`Inventory top data loaded for ${targetDate}: ${results.length} records`);
    
    // Add actual_date to response for frontend awareness
    const response = {
      data: results,
      actual_date: targetDate,
      total_inventory: totalInventory / 1000.0 // Convert to tons
    };
    
    return c.json(response);
  } catch (e: any) {
    console.error(`Error in /api/inventory/top for date ${targetDate}:`, e);
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for inventory summary statistics
app.get('/api/inventory/summary', async (c) => {
  const { date, start_date, end_date } = c.req.query();
  let final_start_date: string | undefined;
  let final_end_date: string | undefined;

  // Validate date parameters: either a single 'date' or a 'start_date'/'end_date' pair is required.
  if (date) {
    final_start_date = date;
    final_end_date = date;
  } else if (start_date && end_date) {
    final_start_date = start_date;
    final_end_date = end_date;
  } else {
    return c.json({
      error: "Invalid date parameters. Please provide either a 'date' parameter or both 'start_date' and 'end_date'."
    }, 400);
  }

  try {
    const filterClause = ProductFilter.getCompleteFilter(false);

    // Query for stats over the date range (product count, avg price)
    const rangePs = c.env.DB.prepare(
      `SELECT
         COUNT(DISTINCT p.product_id) as product_count,
         AVG(dm.average_price) as avg_price
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date BETWEEN ?1 AND ?2
         AND dm.inventory_level IS NOT NULL
         AND dm.inventory_level > 0
         AND ${filterClause}`
    ).bind(final_start_date, final_end_date);

    // Query for end-of-period inventory snapshot
    const snapshotPs = c.env.DB.prepare(
      `SELECT
         SUM(dm.inventory_level) as total_inventory
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.inventory_level IS NOT NULL
         AND dm.inventory_level > 0
         AND ${filterClause}`
    ).bind(final_end_date);

    // Query for Top 15 inventory at the end of the period
    const top15Ps = c.env.DB.prepare(
      `SELECT SUM(inventory_level) as top15_total
       FROM (
         SELECT dm.inventory_level
         FROM DailyMetrics dm
         JOIN Products p ON dm.product_id = p.product_id
         WHERE dm.record_date = ?1
           AND dm.inventory_level IS NOT NULL
           AND dm.inventory_level > 0
           AND ${filterClause}
         ORDER BY dm.inventory_level DESC
         LIMIT 15
       )`
    ).bind(final_end_date);

    const [rangeResult, snapshotResult, top15Result] = await Promise.all([
      rangePs.first<{ product_count: number; avg_price: number }>(),
      snapshotPs.first<{ total_inventory: number }>(),
      top15Ps.first<{ top15_total: number }>()
    ]);

    const totalInventory = snapshotResult?.total_inventory || 0;
    const top15Total = top15Result?.top15_total || 0;
    const top15Percentage = totalInventory > 0 ? (top15Total / totalInventory * 100) : 0;

    const response = {
      total_inventory: totalInventory / 1000, // 转换为吨，保持单位一致性
      top15_total: top15Total / 1000, // 转换为吨，保持单位一致性
      top15_percentage: Math.round(top15Percentage * 100) / 100,
      product_count: rangeResult?.product_count || 0,
      avg_price: rangeResult?.avg_price || 0
    };
    
    console.log(`Inventory summary loaded for ${final_start_date} to ${final_end_date}:`, response);
    return c.json(response);
  } catch (e: any) {
    console.error(`Error in /api/inventory/summary for ${final_start_date}-${final_end_date}:`, e);
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});
// 新增：用于获取未经过滤的总库存的端点
app.get('/api/inventory/total-summary', async (c) => {
  const { date, end_date } = c.req.query();
  let targetDate = end_date || date;

  if (!targetDate) {
    return c.json({
      error: "请提供 'date' 或 'end_date' 参数。"
    }, 400);
  }

  try {
    // 首先尝试查询指定日期的库存数据
    let snapshotPs = c.env.DB.prepare(
      `SELECT
         SUM(inventory_level) as total_inventory
       FROM DailyMetrics
       WHERE record_date = ?1
         AND inventory_level IS NOT NULL
         AND inventory_level > 0`
    ).bind(targetDate);

    let snapshotResult = await snapshotPs.first<{ total_inventory: number }>();
    let totalInventory = snapshotResult?.total_inventory || 0;

    // 如果指定日期没有数据，查找最新可用的库存日期
    if (totalInventory === 0) {
      console.log(`在日期 ${targetDate} 没有找到库存数据，查找最新可用日期...`);
      
      const latestDatePs = c.env.DB.prepare(
        `SELECT record_date
         FROM DailyMetrics
         WHERE inventory_level IS NOT NULL AND inventory_level > 0
         ORDER BY record_date DESC
         LIMIT 1`
      );
      
      const latestDateResult = await latestDatePs.first<{ record_date: string }>();
      
      if (latestDateResult?.record_date) {
        targetDate = latestDateResult.record_date;
        console.log(`找到最新库存日期: ${targetDate}`);
        
        // 使用最新日期重新查询
        snapshotPs = c.env.DB.prepare(
          `SELECT
             SUM(inventory_level) as total_inventory
           FROM DailyMetrics
           WHERE record_date = ?1
             AND inventory_level IS NOT NULL
             AND inventory_level > 0`
        ).bind(targetDate);
        
        snapshotResult = await snapshotPs.first<{ total_inventory: number }>();
        totalInventory = snapshotResult?.total_inventory || 0;
      }
    }

    const response = {
      total_inventory: totalInventory / 1000.0, // 转换为吨单位
      actual_date: targetDate, // 返回实际查询到数据的日期
    };
    
    console.log(`为 ${targetDate} 加载了总库存（未经过滤）:`, response);
    return c.json(response);
  } catch (e: any) {
    console.error(`在 /api/inventory/total-summary 中为 ${targetDate} 查询时出错:`, e);
    return c.json({ error: '数据库查询失败', details: e.message }, 500);
  }
});


// Endpoint for inventory distribution (pie chart data)
app.get('/api/inventory/distribution', async (c) => {
  const { date, limit = '15' } = c.req.query();

  if (!date) {
    return c.json({ error: 'Missing date query parameter' }, 400);
  }

  try {
    // Use centralized filtering logic (for inventory data - excludes fresh products)
    const filterClause = ProductFilter.getCompleteFilter(false);

    // Get total inventory for percentage calculation
    const totalPs = c.env.DB.prepare(
      `SELECT SUM(dm.inventory_level) as total_inventory
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.inventory_level IS NOT NULL
         AND dm.inventory_level > 0
         AND ${filterClause}`
    ).bind(date);

    const totalResult = await totalPs.first();
    const totalInventory = totalResult?.total_inventory || 0;

    const ps = c.env.DB.prepare(
      `SELECT
         p.product_name,
         dm.inventory_level / 1000.0 as inventory_level,
         ROUND((dm.inventory_level * 100.0 / ?3), 2) as percentage
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.inventory_level IS NOT NULL
         AND dm.inventory_level > 0
         AND ${filterClause}
       ORDER BY dm.inventory_level DESC
       LIMIT ?2`
    ).bind(date, parseInt(limit, 10), totalInventory);

    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for inventory trend over time
app.get('/api/inventory/trend', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    // Use centralized filtering logic (for inventory data - excludes fresh products)
    const filterClause = ProductFilter.getCompleteFilter(false);

    const ps = c.env.DB.prepare(
      `SELECT
         dm.record_date,
         SUM(dm.inventory_level) / 1000.0 as total_inventory,
         COUNT(DISTINCT p.product_id) as product_count,
         AVG(dm.inventory_level) / 1000.0 as avg_inventory_per_product
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date BETWEEN ?1 AND ?2
         AND dm.inventory_level IS NOT NULL
         AND dm.inventory_level > 0
         AND ${filterClause}
       GROUP BY dm.record_date
       ORDER BY dm.record_date ASC`
    ).bind(start_date, end_date);

    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for inventory trends, including inventory level and turnover days
app.get('/api/inventory/trends', async (c) => {
  const { start_date, end_date, product_id } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }
  if (!product_id) {
    return c.json({ error: 'Missing product_id query parameter' }, 400);
  }

  try {
    const ps = c.env.DB.prepare(
      `SELECT
         record_date,
         inventory_level,
         inventory_turnover_days
       FROM DailyMetrics
       WHERE record_date BETWEEN ?1 AND ?2
         AND product_id = ?3
       ORDER BY record_date ASC`
    ).bind(start_date, end_date, parseInt(product_id, 10));

    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for the daily sales-to-production ratio trend chart
// Uses centralized ProductionRatioCalculator for consistent results
app.get('/api/trends/ratio', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    const calculator = new ProductionRatioCalculator(c.env.DB);
    
    // Get daily ratios using centralized calculation
    const dailyRatios = await calculator.calculateDailyRatios(start_date, end_date);
    
    // Get authoritative average ratio for consistency
    const avgRatio = await calculator.calculateAggregateRatio(start_date, end_date);
    
    // Validate consistency between calculation methods
    const consistencyCheck = await calculator.validateConsistency(start_date, end_date);
    
    // Format response to maintain backward compatibility
    const results = dailyRatios.map(item => ({
      record_date: item.date,
      daily_sales: item.sales_volume / 1000, // 转换为吨，保持单位一致性
      daily_production: item.production_volume / 1000, // 转换为吨，保持单位一致性
      ratio: item.ratio,
      calculation_method: 'Unified Calculator'
    }));

    console.log(`📈 [API] Trends ratio data calculated for ${start_date} to ${end_date}: ${results.length} days, avg: ${avgRatio}%`);
    
    // Log warning if inconsistency detected
    if (!consistencyCheck.isConsistent) {
      console.warn(`⚠️ [API] Data inconsistency detected in /api/trends/ratio:
        - Aggregate ratio: ${consistencyCheck.aggregateRatio.toFixed(2)}%
        - Daily average ratio: ${consistencyCheck.dailyAverageRatio.toFixed(2)}%
        - Difference: ${consistencyCheck.difference.toFixed(2)}%
        - This inconsistency may affect data visualization and analysis
      `);
    }

    // Include authoritative average in response for frontend consistency
    return c.json({
      daily_data: results,
      avg_ratio: avgRatio,
      total_days: results.length,
      calculation_method: 'Unified with ratio-stats',
      consistency_check: {
        is_consistent: consistencyCheck.isConsistent,
        difference: consistencyCheck.difference
      }
    });
  } catch (e: any) {
    console.error(`❌ [API] Error in /api/trends/ratio:`, e);
    return c.json({ error: 'Failed to calculate production ratio trends', details: e.message }, 500);
  }
});

// Endpoint for validating consistency between different calculation methods
// Used for debugging and monitoring data consistency
app.get('/api/production/validate-consistency', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    const calculator = new ProductionRatioCalculator(c.env.DB);
    const consistencyCheck = await calculator.validateConsistency(start_date, end_date);

    // Also validate that both endpoints return the same average
    const [ratioStats, trendsData] = await Promise.all([
      calculator.calculateStatistics(start_date, end_date),
      calculator.calculateDailyRatios(start_date, end_date)
    ]);

    const endpointConsistency = {
      ratio_stats_avg: ratioStats.avg_ratio,
      trends_avg: ratioStats.avg_ratio, // Both use same calculator now
      endpoints_consistent: true
    };

    console.log(`🔍 [API] Consistency validation for ${start_date} to ${end_date}:`, {
      ...consistencyCheck,
      ...endpointConsistency
    });

    return c.json({
      calculation_consistency: consistencyCheck,
      endpoint_consistency: endpointConsistency,
      validation_timestamp: new Date().toISOString(),
      date_range: { start_date, end_date }
    });
  } catch (e: any) {
    console.error(`❌ [API] Error in consistency validation:`, e);
    return c.json({ error: 'Failed to validate consistency', details: e.message }, 500);
  }
});

// Endpoint for production ratio statistics (avg, min, max)
// Uses centralized ProductionRatioCalculator for consistent results
app.get('/api/production/ratio-stats', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    const calculator = new ProductionRatioCalculator(c.env.DB);
    const statistics = await calculator.calculateStatistics(start_date, end_date);
    
    // Validate consistency between calculation methods
    const consistencyCheck = await calculator.validateConsistency(start_date, end_date);
    
    // Log warning if inconsistency detected
    if (!consistencyCheck.isConsistent) {
      console.warn(`⚠️ [API] Data inconsistency detected in /api/production/ratio-stats:
        - Aggregate ratio: ${consistencyCheck.aggregateRatio.toFixed(2)}%
        - Daily average ratio: ${consistencyCheck.dailyAverageRatio.toFixed(2)}%
        - Difference: ${consistencyCheck.difference.toFixed(2)}%
        - This inconsistency may affect data visualization and analysis
      `);
    }

    console.log(`📊 [API] Production ratio stats calculated for ${start_date} to ${end_date}:`, statistics);

    // Include consistency check in the response
    return c.json({
      ...statistics,
      consistency_check: {
        is_consistent: consistencyCheck.isConsistent,
        difference: consistencyCheck.difference
      }
    });
  } catch (e: any) {
    console.error(`❌ [API] Error in /api/production/ratio-stats:`, e);
    return c.json({ error: 'Failed to calculate production ratio statistics', details: e.message }, 500);
  }
});

// Debug endpoint to check production ratio data
app.get('/api/debug/ratio-data', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    const filterClause = ProductFilter.getCompleteFilter(false);

    // Get summary data
    const summaryPs = c.env.DB.prepare(
      `SELECT
        SUM(dm.sales_volume) as total_sales,
        SUM(dm.production_volume) as total_production,
        (SUM(dm.sales_volume) / SUM(dm.production_volume)) * 100 as overall_ratio,
        COUNT(*) as total_records
      FROM DailyMetrics dm
      JOIN Products p ON dm.product_id = p.product_id
      WHERE dm.record_date BETWEEN ?1 AND ?2
        AND dm.sales_volume IS NOT NULL
        AND dm.production_volume IS NOT NULL
        AND dm.sales_volume > 0
        AND dm.production_volume > 0
        AND ${filterClause}`
    ).bind(start_date, end_date);

    // Get daily data sample
    const dailyPs = c.env.DB.prepare(
      `SELECT
        dm.record_date,
        dm.sales_volume,
        dm.production_volume,
        (dm.sales_volume / dm.production_volume) * 100 as daily_ratio,
        p.product_name
      FROM DailyMetrics dm
      JOIN Products p ON dm.product_id = p.product_id
      WHERE dm.record_date BETWEEN ?1 AND ?2
        AND dm.sales_volume IS NOT NULL
        AND dm.production_volume IS NOT NULL
        AND dm.sales_volume > 0
        AND dm.production_volume > 0
        AND ${filterClause}
      ORDER BY (dm.sales_volume / dm.production_volume) DESC
      LIMIT 10`
    ).bind(start_date, end_date);

    const summary = await summaryPs.first();
    const { results: dailySample } = await dailyPs.all();

    return c.json({
      summary,
      high_ratio_samples: dailySample,
      calculation_explanation: {
        overall_ratio: "总销售量 / 总产量 * 100",
        daily_ratio: "每日销售量 / 每日产量 * 100"
      }
    });
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for the dual-axis sales and price trend chart
// Applies data filtering logic from original Python script
// FIXED: Remove data duplication by using DISTINCT on product_id and record_date
app.get('/api/trends/sales-price', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    // Use centralized filtering logic (for sales data - includes fresh products)
    const filterClause = ProductFilter.getCompleteFilter(true);

    // FIXED: Use subquery to ensure unique product-date combinations to avoid duplication
    const ps = c.env.DB.prepare(
      `SELECT
         unique_daily.record_date,
         SUM(unique_daily.sales_volume) as total_sales,
         SUM(unique_daily.sales_amount) as total_amount,
         CASE
           WHEN SUM(unique_daily.sales_volume) > 0
           THEN SUM(unique_daily.sales_amount) / SUM(unique_daily.sales_volume)
           ELSE 0
         END as avg_price
       FROM (
         SELECT DISTINCT
           dm.record_date,
           dm.product_id,
           dm.sales_volume,
           dm.sales_amount
         FROM DailyMetrics dm
         JOIN Products p ON dm.product_id = p.product_id
         WHERE dm.record_date BETWEEN ?1 AND ?2
           AND dm.sales_volume IS NOT NULL
           AND dm.sales_volume > 0
           AND ${filterClause}
       ) as unique_daily
       GROUP BY unique_daily.record_date
       ORDER BY unique_daily.record_date ASC`
    ).bind(start_date, end_date);

    const { results } = await ps.all();
    
    // Transform the results to match frontend expectations
    const transformedResults = results.map((row: any) => ({
      date: row.record_date,
      volume: (row.total_sales || 0) / 1000.0, // Convert kg to tons
      amount: row.total_amount || 0,
      price: (row.avg_price || 0) * 1000.0 // Convert from yuan/kg to yuan/ton
    }));
    
    console.log(`📊 Sales-price trend data loaded for ${start_date} to ${end_date}: ${transformedResults.length} days`);
    
    return c.json(transformedResults);
  } catch (e: any) {
    console.error('Error in /api/trends/sales-price:', e);
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Sales detail endpoint - returns individual product sales data
app.get('/api/sales', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    // Use centralized filtering logic (for sales data - includes fresh products)
    const filterClause = ProductFilter.getCompleteFilter(true);

    // Use DISTINCT to avoid duplicate records like in trends API
    const ps = c.env.DB.prepare(
      `SELECT DISTINCT
         dm.record_date as date,
         p.product_name,
         dm.sales_volume / 1000.0 as volume,
         dm.sales_amount as amount,
         dm.average_price * 1000.0 as price
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date BETWEEN ?1 AND ?2
         AND dm.sales_volume IS NOT NULL
         AND dm.sales_volume > 0
         AND ${filterClause}
       ORDER BY dm.record_date DESC, dm.sales_volume DESC`
    ).bind(start_date, end_date);

    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Debug endpoint - simplified sales query without filters
app.get('/api/debug/sales-simple', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    const ps = c.env.DB.prepare(
      `SELECT
         dm.record_date,
         SUM(dm.sales_volume) as total_sales,
         SUM(dm.sales_amount) as total_amount,
         CASE
           WHEN SUM(dm.sales_volume) > 0
           THEN SUM(dm.sales_amount) / SUM(dm.sales_volume)
           ELSE 0
         END as avg_price
       FROM DailyMetrics dm
       WHERE dm.record_date BETWEEN ?1 AND ?2
         AND dm.sales_volume IS NOT NULL
         AND dm.sales_volume > 0
       GROUP BY dm.record_date
       ORDER BY dm.record_date ASC`
    ).bind(start_date, end_date);

    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Debug endpoint - check individual sales_amount values
app.get('/api/debug/sales-raw', async (c) => {
  const { start_date, end_date } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    const ps = c.env.DB.prepare(
      `SELECT
         dm.record_date,
         dm.sales_volume,
         dm.sales_amount,
         typeof(dm.sales_amount) as amount_type
       FROM DailyMetrics dm
       WHERE dm.record_date BETWEEN ?1 AND ?2
         AND dm.sales_volume IS NOT NULL
         AND dm.sales_volume > 0
       ORDER BY dm.record_date ASC
       LIMIT 10`
    ).bind(start_date, end_date);

    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Debug endpoint - test database connection and count records
app.get('/api/debug/db-test', async (c) => {
  try {
    // Test basic connection
    const countPs = c.env.DB.prepare('SELECT COUNT(*) as total FROM DailyMetrics');
    const { results: countResults } = await countPs.all();

    // Test sales amount count
    const salesPs = c.env.DB.prepare('SELECT COUNT(*) as sales_count FROM DailyMetrics WHERE sales_amount IS NOT NULL AND sales_amount > 0');
    const { results: salesResults } = await salesPs.all();

    // Test a simple sales amount query
    const samplePs = c.env.DB.prepare('SELECT record_date, sales_volume, sales_amount FROM DailyMetrics WHERE sales_amount IS NOT NULL AND sales_amount > 0 LIMIT 3');
    const { results: sampleResults } = await samplePs.all();

    return c.json({
      total_records: countResults[0],
      sales_records: salesResults[0],
      sample_data: sampleResults
    });
  } catch (e: any) {
    return c.json({ error: 'Database test failed', details: e.message }, 500);
  }
});

// Debug endpoint - analyze specific date discrepancy
app.get('/api/debug/analyze-date', async (c) => {
  const { date = '2025-07-10' } = c.req.query();
  
  try {
    const filterClause = ProductFilter.getCompleteFilter(false);
    
    // Query 1: With filters (current API logic)
    const filteredPs = c.env.DB.prepare(
      `SELECT
         dm.record_date,
         SUM(dm.sales_volume) as total_sales_filtered,
         COUNT(*) as filtered_records,
         COUNT(DISTINCT p.product_id) as filtered_products
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.sales_volume IS NOT NULL
         AND dm.sales_volume > 0
         AND ${filterClause}
       GROUP BY dm.record_date`
    ).bind(date);

    // Query 2: Without filters (all data)
    const unfilteredPs = c.env.DB.prepare(
      `SELECT
         dm.record_date,
         SUM(dm.sales_volume) as total_sales_unfiltered,
         COUNT(*) as unfiltered_records,
         COUNT(DISTINCT dm.product_id) as unfiltered_products
       FROM DailyMetrics dm
       WHERE dm.record_date = ?1
         AND dm.sales_volume IS NOT NULL
         AND dm.sales_volume > 0
       GROUP BY dm.record_date`
    ).bind(date);

    // Query 3: Check for duplicate data
    const duplicateCheckPs = c.env.DB.prepare(
      `SELECT
         dm.product_id,
         p.product_name,
         dm.sales_volume,
         COUNT(*) as occurrence_count
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.sales_volume IS NOT NULL
         AND dm.sales_volume > 0
       GROUP BY dm.product_id, dm.sales_volume
       HAVING COUNT(*) > 1
       ORDER BY COUNT(*) DESC, dm.sales_volume DESC
       LIMIT 10`
    ).bind(date);

    // Query 4: Show unique products with their total sales (corrected)
    const correctedDataPs = c.env.DB.prepare(
      `SELECT
         dm.product_id,
         p.product_name,
         dm.sales_volume,
         p.category,
         CASE WHEN p.product_name LIKE '%鲜%' AND p.product_name NOT LIKE '%凤肠%' THEN 'FILTERED' ELSE 'INCLUDED' END as filter_status
       FROM DailyMetrics dm
       JOIN Products p ON dm.product_id = p.product_id
       WHERE dm.record_date = ?1
         AND dm.sales_volume IS NOT NULL
         AND dm.sales_volume > 0
         AND ${filterClause}
       GROUP BY dm.product_id
       ORDER BY dm.sales_volume DESC
       LIMIT 20`
    ).bind(date);

    // Query 5: Calculate corrected total (sum distinct product sales)
    const correctedTotalPs = c.env.DB.prepare(
      `SELECT
         SUM(dm.sales_volume) as corrected_total_sales
       FROM (
         SELECT DISTINCT dm.product_id, dm.sales_volume
         FROM DailyMetrics dm
         JOIN Products p ON dm.product_id = p.product_id
         WHERE dm.record_date = ?1
           AND dm.sales_volume IS NOT NULL
           AND dm.sales_volume > 0
           AND ${filterClause}
       ) as unique_sales`
    ).bind(date);

    const [filteredResult, unfilteredResult, duplicates, correctedData, correctedTotal] = await Promise.all([
      filteredPs.first<{ total_sales_filtered: number; filtered_records: number; filtered_products: number }>(),
      unfilteredPs.first<{ total_sales_unfiltered: number; unfiltered_records: number; unfiltered_products: number }>(),
      duplicateCheckPs.all(),
      correctedDataPs.all(),
      correctedTotalPs.first<{ corrected_total_sales: number }>()
    ]);

    return c.json({
      analysis_date: date,
      current_api_result: filteredResult || { total_sales_filtered: 0, filtered_records: 0, filtered_products: 0 },
      all_data_result: unfilteredResult || { total_sales_unfiltered: 0, unfiltered_records: 0, unfiltered_products: 0 },
      corrected_total: correctedTotal?.corrected_total_sales || 0,
      data_quality_issues: {
        total_records: filteredResult?.filtered_records || 0,
        unique_products: filteredResult?.filtered_products || 0,
        duplicates_detected: (duplicates.results || []).length > 0,
        duplicate_examples: duplicates.results || []
      },
      difference_analysis: {
        current_vs_corrected: (filteredResult?.total_sales_filtered || 0) - (correctedTotal?.corrected_total_sales || 0),
        explanation: "If positive, indicates data duplication inflating the total"
      },
      corrected_product_data: correctedData.results || [],
      filter_explanation: {
        product_name_filter: "Excludes products containing '鲜' except those containing '凤肠'",
        category_filter: "Excludes '副产品' and '生鲜品其他' categories"
      }
    });
  } catch (e: any) {
    return c.json({ error: 'Analysis failed', details: e.message }, 500);
  }
});

// Endpoint to get price change records with filtering
// Implements the same logic as the original Python script's PriceAnalyzer
app.get('/api/price-changes', async (c) => {
  const { start_date, end_date, min_price_diff = '200' } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    // Use centralized filtering logic (relaxed mode for consistency)
    const filterClause = ProductFilter.getPriceAdjustmentFilter(false);

    const ps = c.env.DB.prepare(
      `SELECT
         pa.adjustment_date,
         pa.product_name,
         pa.specification,
         pa.adjustment_count,
         pa.previous_price * 1000.0 as previous_price,
         pa.current_price * 1000.0 as current_price,
         pa.price_difference * 1000.0 as price_difference,
         pa.category
       FROM PriceAdjustments pa
       JOIN Products p ON pa.product_id = p.product_id
       WHERE pa.adjustment_date BETWEEN ?1 AND ?2
         AND ABS(pa.price_difference) >= (?3 / 1000.0)
         AND ${filterClause}
       ORDER BY pa.adjustment_date DESC, ABS(pa.price_difference) DESC`
    ).bind(start_date, end_date, parseFloat(min_price_diff));

    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint to get price trend data for charts
app.get('/api/price-trends', async (c) => {
  const { start_date, end_date, product_name } = c.req.query();

  if (!start_date || !end_date) {
    return c.json({ error: 'Missing start_date or end_date query parameters' }, 400);
  }

  try {
    // Use centralized filtering logic (relaxed mode for consistency)
    const filterClause = ProductFilter.getPriceAdjustmentFilter(false);

    let query = `
      SELECT
        pa.adjustment_date,
        pa.product_name,
        pa.current_price * 1000.0 as current_price,
        pa.price_difference * 1000.0 as price_difference
      FROM PriceAdjustments pa
      JOIN Products p ON pa.product_id = p.product_id
      WHERE pa.adjustment_date BETWEEN ?1 AND ?2
        AND ${filterClause}
    `;

    const params = [start_date, end_date];

    if (product_name) {
      query += ` AND pa.product_name = ?3`;
      params.push(product_name);
    }

    query += ` ORDER BY pa.adjustment_date ASC`;

    const ps = c.env.DB.prepare(query).bind(...params);
    const { results } = await ps.all();
    return c.json(results);
  } catch (e: any) {
    return c.json({ error: 'Database query failed', details: e.message }, 500);
  }
});

// Endpoint for processing price adjustment Excel files (调价表.xlsx)
// Implements the same logic as the original Python script's DataLoader.preprocess_sheet
app.post('/api/upload/price-adjustments', async (c) => {
  try {
    const formData = await c.req.formData();
    const file = formData.get('file');

    if (!file || !(file instanceof File)) {
      return c.json({ error: 'No file uploaded' }, 400);
    }

    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const db = c.env.DB;

    let processedRecords = 0;
    const errors: string[] = [];

    // Process each sheet (following original Python logic)
    for (const sheetName of workbook.SheetNames) {
      try {
        // Extract date info from sheet name (价格表4月2号（2）)
        const dateMatch = sheetName.match(/价格表(\d+)月(\d+)号(?:（(\d+)）)?/);
        if (!dateMatch) {
          errors.push(`Cannot extract date from sheet name: ${sheetName}`);
          continue;
        }

        const month = parseInt(dateMatch[1]);
        const day = parseInt(dateMatch[2]);
        const adjustmentCount = parseInt(dateMatch[3]) || 1;
        const adjustmentDate = `2025-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

        const worksheet = workbook.Sheets[sheetName];
        const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        if (!rawData || rawData.length === 0) {
          continue;
        }

        // Process three templates side by side (columns 0-8, 9-17, 18-26)
        const templates = [
          { start: 0, end: 9 },
          { start: 9, end: 18 },
          { start: 18, end: 27 }
        ];

        const stmts: any[] = [];

        for (const template of templates) {
          for (let i = 1; i < rawData.length; i++) { // Skip header row
            const row = rawData[i] as any[];
            if (!row || row.length <= template.start) continue;

            const category = row[template.start];
            const productName = row[template.start + 1];
            const specification = row[template.start + 2];
            const previousPrice = parseFloat(row[template.start + 7]) || null; // 加工二厂-前价格
            const currentPrice = parseFloat(row[template.start + 8]) || null;   // 加工二厂-价格

            // Skip invalid rows
            if (!productName ||
                typeof productName !== 'string' ||
                productName.includes('均价') ||
                productName.includes('品名') ||
                !currentPrice) {
              continue;
            }

            const priceDifference = previousPrice ? currentPrice - previousPrice : 0;

            // Find or create product
            const productQuery = db.prepare('SELECT product_id FROM Products WHERE product_name = ?').bind(productName);
            let productResult = await productQuery.first<{ product_id: number }>();

            if (!productResult) {
              const insertProduct = db.prepare('INSERT INTO Products (product_name, category) VALUES (?, ?) RETURNING product_id')
                .bind(productName, category || null);
              productResult = await insertProduct.first<{ product_id: number }>();
            }
            
            if (!productResult) {
              errors.push(`Failed to find or create product: ${productName}`);
              continue;
            }

            const productId = productResult.product_id;

            // Insert price adjustment record
            const stmt = db.prepare(`
              INSERT INTO PriceAdjustments
              (adjustment_date, product_id, product_name, specification, adjustment_count,
               previous_price, current_price, price_difference, category)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `).bind(
              adjustmentDate,
              productId,
              productName,
              specification || null,
              adjustmentCount,
              previousPrice,
              currentPrice,
              priceDifference,
              category || null
            );

            stmts.push(stmt);
          }
        }

        if (stmts.length > 0) {
          await db.batch(stmts);
          processedRecords += stmts.length;
        }

      } catch (sheetError: any) {
        errors.push(`Error processing sheet ${sheetName}: ${sheetError.message}`);
      }
    }

    return c.json({
      message: 'Price adjustment data processed successfully',
      processedRecords,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (e: any) {
    console.error('Price adjustment upload error:', e);
    return c.json({ error: 'Internal server error', details: e.message }, 500);
  }
});

// --- Data Validation Layer ---

/**
 * Data validation utilities for upload endpoints
 */
class DataValidator {
  /**
   * Validate DailyMetrics row data
   */
  static validateDailyMetricsRow(row: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields validation
    if (!row.product_id || isNaN(parseInt(row.product_id))) {
      errors.push('Invalid or missing product_id');
    }

    if (!row.record_date) {
      errors.push('Missing record_date');
    } else {
      // Validate date format
      const date = new Date(row.record_date);
      if (isNaN(date.getTime())) {
        errors.push('Invalid record_date format');
      }
    }

    // Numeric fields validation (allow null/undefined for optional fields)
    const numericFields = ['production_volume', 'sales_volume', 'inventory_level', 'average_price'];
    for (const field of numericFields) {
      if (row[field] !== null && row[field] !== undefined && row[field] !== '') {
        const value = parseFloat(row[field]);
        if (isNaN(value) || value < 0) {
          errors.push(`Invalid ${field}: must be a non-negative number`);
        }
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Sanitize and clean row data
   */
  static sanitizeDailyMetricsRow(row: any): any {
    return {
      product_id: parseInt(row.product_id),
      record_date: row.record_date,
      production_volume: row.production_volume ? parseFloat(row.production_volume) : null,
      sales_volume: row.sales_volume ? parseFloat(row.sales_volume) : null,
      inventory_level: row.inventory_level ? parseFloat(row.inventory_level) : null,
      average_price: row.average_price ? parseFloat(row.average_price) : null
    };
  }
}

// Endpoint for handling Excel file uploads with enhanced validation
app.post('/api/upload', async (c) => {
  try {
    const formData = await c.req.formData();
    const file = formData.get('file');

    if (!file || !(file instanceof File)) {
      return c.json({ error: 'No file uploaded' }, 400);
    }

    const buffer = await file.arrayBuffer();
    const workbook = XLSX.read(buffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const rows = XLSX.utils.sheet_to_json(worksheet);

    if (rows.length === 0) {
      return c.json({ error: 'Excel file is empty or in the wrong format' }, 400);
    }

    const db = c.env.DB;
    const validRows: any[] = [];
    const errors: string[] = [];

    // Validate and sanitize each row
    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const validation = DataValidator.validateDailyMetricsRow(row);

      if (validation.isValid) {
        validRows.push(DataValidator.sanitizeDailyMetricsRow(row));
      } else {
        errors.push(`Row ${i + 1}: ${validation.errors.join(', ')}`);
      }
    }

    // If too many errors, reject the upload
    if (errors.length > rows.length * 0.1) { // Allow up to 10% error rate
      return c.json({
        error: 'Too many validation errors in uploaded data',
        details: errors.slice(0, 10), // Show first 10 errors
        totalErrors: errors.length,
        totalRows: rows.length
      }, 400);
    }

    if (validRows.length === 0) {
      return c.json({ error: 'No valid rows found in uploaded data' }, 400);
    }

    // Prepare batch insert statements
    const stmts = validRows.map(row => {
      return db.prepare(
        'INSERT INTO DailyMetrics (product_id, record_date, production_volume, sales_volume, inventory_level, average_price) VALUES (?, ?, ?, ?, ?, ?)'
      ).bind(
        row.product_id,
        row.record_date,
        row.production_volume,
        row.sales_volume,
        row.inventory_level,
        row.average_price
      );
    });

    const batchResult = await db.batch(stmts);

    return c.json({
      message: 'Upload successful',
      results: batchResult,
      processedRows: validRows.length,
      skippedRows: errors.length,
      errors: errors.length > 0 ? errors.slice(0, 5) : undefined // Show first 5 errors if any
    });

  } catch (e: any) {
    console.error('Upload error:', e);
    return c.json({ error: 'Internal server error', details: e.message }, 500);
  }
});

// Admin endpoint for batch data import
app.post('/api/admin/import-batch', async (c) => {
  try {
    const { data } = await c.req.json();

    if (!Array.isArray(data)) {
      return c.json({ error: 'Data must be an array' }, 400);
    }

    // Prepare batch insert
    const stmt = c.env.DB.prepare(`
      INSERT INTO DailyMetrics (
        record_date, product_id, production_volume, sales_volume,
        inventory_level, average_price, sales_amount
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    // Execute batch insert
    const results = [];
    for (const record of data) {
      try {
        const result = await stmt.bind(
          record.record_date,
          record.product_id,
          record.production_volume,
          record.sales_volume,
          record.inventory_level,
          record.average_price,
          record.sales_amount
        ).run();
        results.push(result);
      } catch (e: any) {
        console.error('Insert error:', e.message);
        // Continue with other records
      }
    }

    return c.json({
      success: true,
      inserted: results.length,
      total: data.length
    });
  } catch (e: any) {
    return c.json({ error: 'Import failed', details: e.message }, 500);
  }
});

// --- Price Monitoring System ---

/**
 * Price Alert Detection Engine
 * Implements intelligent price monitoring and alert generation
 */
class PriceAlertEngine {
  /**
   * Detect daily price drops based on configured thresholds
   */
  static async detectDailyDrop(db: D1Database, date: string): Promise<any[]> {
    const query = `
      SELECT
        pa.product_id,
        pa.product_name,
        pa.current_price,
        pa.previous_price,
        pa.price_difference,
        pa.adjustment_date,
        ROUND((pa.price_difference / NULLIF(pa.previous_price, 0)) * 100, 2) as change_percentage
      FROM PriceAdjustments pa
      WHERE pa.adjustment_date = ?
        AND pa.price_difference < 0
        AND (
          ABS(pa.price_difference) > 200
          OR
          ABS(pa.price_difference / NULLIF(pa.previous_price, 0)) > 0.05
        )
        AND ${ProductFilter.getPriceAdjustmentFilter()}
    `;

    const { results } = await db.prepare(query).bind(date).all<DailyDropRow>();

    return (results || []).map((row: DailyDropRow) => ({
      alert_type: 'DAILY_DROP',
      alert_level: 'WARNING',
      product_id: row.product_id,
      product_name: row.product_name,
      current_price: row.current_price,
      previous_price: row.previous_price,
      price_change: row.price_difference,
      change_percentage: row.change_percentage,
      alert_message: `${row.product_name}价格单日下降${Math.abs(row.change_percentage)}%，降幅${Math.abs(row.price_difference)}元/吨`,
      alert_date: date
    }));
  }

  /**
   * Detect consecutive price drops
   */
  static async detectConsecutiveDrop(db: D1Database, productId: number, days: number = 3): Promise<any | null> {
    const query = `
      SELECT
        pa.adjustment_date,
        pa.current_price,
        pa.previous_price,
        pa.price_difference,
        pa.product_name
      FROM PriceAdjustments pa
      WHERE pa.product_id = ?
        AND pa.price_difference < 0
        AND ${ProductFilter.getPriceAdjustmentFilter()}
      ORDER BY pa.adjustment_date DESC
      LIMIT ?
    `;

    const { results } = await db.prepare(query).bind(productId, days).all<ConsecutiveDropRow>();

    if (results && results.length >= days) {
      const consecutiveDays = results.length;
      const totalDrop = results.reduce((sum: number, row: ConsecutiveDropRow) => sum + Math.abs(row.price_difference), 0);
      const latestRecord = results[0];

      return {
        alert_type: 'CONSECUTIVE_DROP',
        alert_level: 'CRITICAL',
        product_id: productId,
        product_name: latestRecord.product_name,
        current_price: latestRecord.current_price,
        previous_price: latestRecord.previous_price,
        price_change: -totalDrop,
        change_percentage: latestRecord.previous_price > 0 ? -(totalDrop / latestRecord.previous_price) * 100 : 0,
        alert_message: `${latestRecord.product_name}连续${consecutiveDays}天价格下降，累计降幅${totalDrop.toFixed(2)}元/吨`,
        alert_date: latestRecord.adjustment_date
      };
    }

    return null;
  }

  /**
   * Detect price volatility anomalies
   */
  static async detectVolatility(db: D1Database, productId: number, days: number = 7): Promise<any | null> {
    const query = `
      SELECT
        pa.current_price,
        pa.adjustment_date,
        pa.product_name
      FROM PriceAdjustments pa
      WHERE pa.product_id = ?
        AND pa.adjustment_date >= date('now', '-${days} days')
        AND ${ProductFilter.getPriceAdjustmentFilter()}
      ORDER BY pa.adjustment_date DESC
    `;

    const { results } = await db.prepare(query).bind(productId).all<VolatilityRow>();

    if (results && results.length >= 3) {
      const prices = results.map((row: VolatilityRow) => row.current_price);
      const mean = prices.reduce((sum: number, price: number) => sum + price, 0) / prices.length;
      const variance = prices.reduce((sum: number, price: number) => sum + Math.pow(price - mean, 2), 0) / prices.length;
      const stdDev = Math.sqrt(variance);
      const volatility = mean > 0 ? stdDev / mean : 0;

      if (volatility > 0.15) {
        const latestRecord = results[0];

        return {
          alert_type: 'VOLATILITY',
          alert_level: 'INFO',
          product_id: productId,
          product_name: latestRecord.product_name,
          current_price: latestRecord.current_price,
          previous_price: mean,
          price_change: latestRecord.current_price - mean,
          change_percentage: mean > 0 ? ((latestRecord.current_price - mean) / mean) * 100 : 0,
          alert_message: `${latestRecord.product_name}最近${days}天价格波动率${(volatility * 100).toFixed(2)}%，超过正常范围`,
          alert_date: latestRecord.adjustment_date
        };
      }
    }

    return null;
  }

  /**
   * Run complete alert detection for a given date
   */
  static async runAlertDetection(db: D1Database, date: string): Promise<void> {
    try {
      const alerts: any[] = [];

      // 1. Detect daily drops
      const dailyDropAlerts = await this.detectDailyDrop(db, date);
      alerts.push(...dailyDropAlerts);

      // 2. Get all products that had price adjustments on this date
      const productsQuery = `
        SELECT DISTINCT product_id 
        FROM PriceAdjustments 
        WHERE adjustment_date = ?
          AND ${ProductFilter.getPriceAdjustmentFilter()}
      `;
      const { results: products } = await db.prepare(productsQuery).bind(date).all<ProductIdRow>();

      // 3. Check for consecutive drops and volatility for each product
      for (const product of products || []) {
        const consecutiveAlert = await this.detectConsecutiveDrop(db, product.product_id);
        if (consecutiveAlert) {
          alerts.push(consecutiveAlert);
        }

        const volatilityAlert = await this.detectVolatility(db, product.product_id);
        if (volatilityAlert) {
          alerts.push(volatilityAlert);
        }
      }

      // 4. Save alerts to database
      for (const alert of alerts) {
        await this.saveAlert(db, alert);
      }

      console.log(`Alert detection completed for ${date}, generated ${alerts.length} alerts`);

    } catch (error) {
      console.error('Alert detection failed:', error);
    }
  }

  /**
   * Save alert to database
   */
  static async saveAlert(db: D1Database, alert: any): Promise<void> {
    const insertQuery = `
      INSERT INTO PriceAlerts (
        alert_date, product_id, product_name, alert_type, alert_level,
        current_price, previous_price, price_change, change_percentage, alert_message
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await db.prepare(insertQuery).bind(
      alert.alert_date,
      alert.product_id,
      alert.product_name,
      alert.alert_type,
      alert.alert_level,
      alert.current_price,
      alert.previous_price,
      alert.price_change,
      alert.change_percentage,
      alert.alert_message
    ).run();
  }
}

// --- Price Monitoring API Endpoints ---

// Get price trends data
app.get('/api/pricing/trends', async (c) => {
  try {
    const { product_id, product_name, start_date, end_date, limit = '100', include_alerts } = c.req.query();

    // Build WHERE conditions
    let whereConditions = ['1=1'];
    const params: any[] = [];

    if (product_id) {
      whereConditions.push('pa.product_id = ?');
      params.push(parseInt(product_id));
    }

    if (product_name) {
      whereConditions.push('pa.product_name LIKE ?');
      params.push(`%${product_name}%`);
    }

    if (start_date) {
      whereConditions.push('pa.adjustment_date >= ?');
      params.push(start_date);
    }

    if (end_date) {
      whereConditions.push('pa.adjustment_date <= ?');
      params.push(end_date);
    }

    // Apply product filtering logic
    whereConditions.push(ProductFilter.getPriceAdjustmentFilter());

    const whereClause = whereConditions.join(' AND ');

    // Query price trends data
    const trendsQuery = `
      SELECT
        pa.adjustment_date,
        pa.product_id,
        pa.product_name,
        pa.specification,
        pa.current_price,
        pa.previous_price,
        pa.price_difference,
        (pa.current_price - pa.previous_price) / pa.previous_price * 100 as change_percentage,
        pa.adjustment_count,
        pa.category
      FROM PriceAdjustments pa
      WHERE ${whereClause}
      ORDER BY pa.adjustment_date DESC, pa.product_name ASC
      LIMIT ?
    `;

    const trends = await c.env.DB.prepare(trendsQuery)
      .bind(...params, parseInt(limit.toString()))
      .all();

    // Query summary statistics
    const summaryQuery = `
      SELECT
        COUNT(DISTINCT pa.product_id) as total_products,
        MIN(pa.adjustment_date) as start_date,
        MAX(pa.adjustment_date) as end_date,
        AVG(pa.current_price) as avg_price,
        MIN(pa.current_price) as min_price,
        MAX(pa.current_price) as max_price,
        COUNT(*) as total_adjustments
      FROM PriceAdjustments pa
      WHERE ${whereClause}
    `;

    const summary = await c.env.DB.prepare(summaryQuery)
      .bind(...params)
      .first<PriceTrendsSummary>();

    const response: any = {
      success: true,
      data: {
        trends: trends.results || [],
        summary: {
          total_products: summary?.total_products || 0,
          date_range: {
            start_date: summary?.start_date || '',
            end_date: summary?.end_date || ''
          },
          price_stats: {
            avg_price: Math.round((summary?.avg_price || 0) * 100) / 100,
            min_price: summary?.min_price || 0,
            max_price: summary?.max_price || 0,
            total_adjustments: summary?.total_adjustments || 0
          }
        }
      }
    };

    // Include alerts if requested
    if (include_alerts === 'true') {
      const alertsQuery = `
        SELECT * FROM PriceAlerts
        WHERE alert_date >= ? AND alert_date <= ?
        ORDER BY alert_date DESC, alert_level DESC
        LIMIT 50
      `;

      const alerts = await c.env.DB.prepare(alertsQuery)
        .bind(start_date || '2024-01-01', end_date || '2025-12-31')
        .all();

      response.data.alerts = alerts.results || [];
    }

    return c.json(response);

  } catch (error) {
    console.error('Error fetching price trends:', error);
    return c.json({
      success: false,
      message: '获取价格趋势数据失败'
    }, 500);
  }
});

// Get price alerts
app.get('/api/pricing/alerts', async (c) => {
  try {
    const { alert_type, alert_level, start_date, end_date, acknowledged, limit = '50' } = c.req.query();

    let whereConditions = ['1=1'];
    const params: any[] = [];

    if (alert_type) {
      whereConditions.push('alert_type = ?');
      params.push(alert_type);
    }

    if (alert_level) {
      whereConditions.push('alert_level = ?');
      params.push(alert_level);
    }

    if (start_date) {
      whereConditions.push('alert_date >= ?');
      params.push(start_date);
    }

    if (end_date) {
      whereConditions.push('alert_date <= ?');
      params.push(end_date);
    }

    if (acknowledged !== undefined) {
      whereConditions.push('is_acknowledged = ?');
      params.push(acknowledged === 'true' ? 1 : 0);
    }

    const whereClause = whereConditions.join(' AND ');

    const alertsQuery = `
      SELECT
        alert_id,
        alert_date,
        product_id,
        product_name,
        alert_type,
        alert_level,
        current_price,
        previous_price,
        price_change,
        change_percentage,
        alert_message,
        is_acknowledged,
        created_at
      FROM PriceAlerts
      WHERE ${whereClause}
      ORDER BY alert_date DESC, alert_level DESC, created_at DESC
      LIMIT ?
    `;

    const alerts = await c.env.DB.prepare(alertsQuery)
      .bind(...params, parseInt(limit))
      .all();

    // Get summary statistics
    const summaryQuery = `
      SELECT
        COUNT(*) as total_alerts,
        SUM(CASE WHEN is_acknowledged = 0 THEN 1 ELSE 0 END) as unacknowledged_count,
        SUM(CASE WHEN alert_level = 'CRITICAL' THEN 1 ELSE 0 END) as critical_count,
        SUM(CASE WHEN alert_level = 'WARNING' THEN 1 ELSE 0 END) as warning_count,
        SUM(CASE WHEN alert_level = 'INFO' THEN 1 ELSE 0 END) as info_count
      FROM PriceAlerts
      WHERE ${whereClause}
    `;

    const summary = await c.env.DB.prepare(summaryQuery)
      .bind(...params)
      .first();

    return c.json({
      success: true,
      data: {
        alerts: alerts.results || [],
        summary: {
          total_alerts: summary?.total_alerts || 0,
          unacknowledged_count: summary?.unacknowledged_count || 0,
          critical_count: summary?.critical_count || 0,
          warning_count: summary?.warning_count || 0,
          info_count: summary?.info_count || 0
        }
      }
    });

  } catch (error) {
    console.error('Error fetching price alerts:', error);
    return c.json({
      success: false,
      message: '获取价格预警数据失败'
    }, 500);
  }
});

// Get price change rankings
app.get('/api/pricing/rankings', async (c) => {
  try {
    const { period = 'daily', start_date, end_date, ranking_type = 'drop_amount', limit = '20' } = c.req.query();

    const endDate = end_date || new Date().toISOString().split('T')[0];
    let startDate = start_date;

    if (!startDate) {
      const date = new Date();
      if (period === 'weekly') {
        date.setDate(date.getDate() - 7);
      } else if (period === 'monthly') {
        date.setMonth(date.getMonth() - 1);
      } else {
        date.setDate(date.getDate() - 1);
      }
      startDate = date.toISOString().split('T')[0];
    }

    const orderBy = ranking_type === 'drop_percentage' 
      ? 'total_drop_percentage ASC' 
      : 'total_drop_amount ASC';

    const rankingsQuery = `
      SELECT
        pa.product_id,
        pa.product_name,
        pa.category,
        SUM(CASE WHEN pa.price_difference < 0 THEN ABS(pa.price_difference) ELSE 0 END) as total_drop_amount,
        AVG(CASE WHEN pa.price_difference < 0 AND pa.previous_price > 0 
            THEN ABS(pa.price_difference / pa.previous_price) * 100 
            ELSE 0 END) as total_drop_percentage,
        COUNT(*) as adjustment_count,
        MAX(pa.current_price) as latest_price,
        MIN(pa.current_price) as period_start_price
      FROM PriceAdjustments pa
      WHERE pa.adjustment_date BETWEEN ? AND ?
        AND pa.price_difference < 0
        AND ${ProductFilter.getPriceAdjustmentFilter()}
      GROUP BY pa.product_id, pa.product_name, pa.category
      HAVING total_drop_amount > 0
      ORDER BY ${orderBy}
      LIMIT ?
    `;

    const rankings = await c.env.DB.prepare(rankingsQuery)
      .bind(startDate, endDate, parseInt(limit))
      .all();

    // Add rank numbers
    const rankedResults = (rankings.results || []).map((item: any, index: number) => ({
      rank: index + 1,
      ...item
    }));

    return c.json({
      success: true,
      data: {
        rankings: rankedResults,
        period_info: {
          start_date: startDate,
          end_date: endDate,
          period: period
        }
      }
    });

  } catch (error) {
    console.error('Error fetching price rankings:', error);
    return c.json({
      success: false,
      message: '获取价格变化排行榜失败'
    }, 500);
  }
});

// Get alert configurations
app.get('/api/pricing/alert-configs', async (c) => {
  try {
    const configsQuery = `
      SELECT * FROM PriceAlertConfigs
      ORDER BY created_at DESC
    `;

    const configs = await c.env.DB.prepare(configsQuery).all();

    const activeConfigQuery = `
      SELECT * FROM PriceAlertConfigs
      WHERE is_active = 1
      LIMIT 1
    `;

    const activeConfig = await c.env.DB.prepare(activeConfigQuery).first();

    return c.json({
      success: true,
      data: {
        configs: configs.results || [],
        active_config: activeConfig || null
      }
    });

  } catch (error) {
    console.error('Error fetching alert configs:', error);
    return c.json({
      success: false,
      message: '获取预警配置失败'
    }, 500);
  }
});

// Create or update alert configuration
app.post('/api/pricing/alert-configs', async (c) => {
  try {
    const config = await c.req.json();

    const insertQuery = `
      INSERT INTO PriceAlertConfigs (
        config_name, daily_drop_threshold, amount_drop_threshold,
        consecutive_days, volatility_threshold, is_active
      ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = await c.env.DB.prepare(insertQuery).bind(
      config.config_name,
      config.daily_drop_threshold,
      config.amount_drop_threshold,
      config.consecutive_days,
      config.volatility_threshold,
      config.is_active ? 1 : 0
    ).run();

    return c.json({
      success: true,
      message: '预警配置创建成功',
      config_id: result.meta.last_row_id
    });

  } catch (error) {
    console.error('Error creating alert config:', error);
    return c.json({
      success: false,
      message: '创建预警配置失败'
    }, 500);
  }
});

// Run alert detection
app.post('/api/pricing/run-alert-detection', async (c) => {
  try {
    const { date } = await c.req.json();
    const targetDate = date || new Date().toISOString().split('T')[0];

    await PriceAlertEngine.runAlertDetection(c.env.DB, targetDate);

    return c.json({
      success: true,
      message: `预警检测完成，检测日期：${targetDate}`
    });

  } catch (error) {
    console.error('Error running alert detection:', error);
    return c.json({
      success: false,
      message: '预警检测失败'
    }, 500);
  }
});

// Acknowledge alert
app.post('/api/pricing/alerts/:alertId/acknowledge', async (c) => {
  try {
    const alertId = c.req.param('alertId');

    const updateQuery = `
      UPDATE PriceAlerts 
      SET is_acknowledged = 1 
      WHERE alert_id = ?
    `;

    await c.env.DB.prepare(updateQuery).bind(parseInt(alertId)).run();

    return c.json({
      success: true,
      message: '预警已确认'
    });

  } catch (error) {
    console.error('Error acknowledging alert:', error);
    return c.json({
      success: false,
      message: '确认预警失败'
    }, 500);
  }
});

import prices from './prices';
app.route('/api/prices', prices);

export default app;
