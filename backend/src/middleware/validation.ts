/**
 * 验证中间件
 * 提供统一的请求参数验证功能
 */

import type { Context, Next } from 'hono';
import { DataValidator } from '../utils/validators';
import { ValidationError } from './errorHandler';
import type { DateRangeQuery, InventoryQuery, ProductionQuery, PricingQuery } from '../types/api';

/**
 * 验证查询参数中间件
 */
export const validateQuery = (requiredFields: string[] = [], optionalFields: string[] = []) => {
  return async (c: Context, next: Next) => {
    const query = c.req.query();
    const validation = DataValidator.validateQueryParams(query, requiredFields);
    
    if (!validation.isValid) {
      throw new ValidationError('查询参数验证失败', validation.errors);
    }
    
    await next();
  };
};

/**
 * 验证日期范围参数中间件
 */
export const validateDateRange = (required: boolean = true) => {
  return async (c: Context, next: Next) => {
    const { start_date, end_date } = c.req.query();
    
    if (required && (!start_date || !end_date)) {
      throw new ValidationError('缺少必需的日期范围参数', ['start_date and end_date are required']);
    }
    
    if (start_date && end_date) {
      const validation = DataValidator.validateDateRange(start_date, end_date);
      if (!validation.isValid) {
        throw new ValidationError('日期范围验证失败', validation.errors);
      }
    }
    
    await next();
  };
};

/**
 * 验证库存查询参数中间件
 */
export const validateInventoryQuery = () => {
  return async (c: Context, next: Next) => {
    const query = c.req.query() as InventoryQuery;
    const errors: string[] = [];
    
    // 验证日期参数
    if (query.date && isNaN(new Date(query.date).getTime())) {
      errors.push('Invalid date format');
    }
    
    if (query.start_date && isNaN(new Date(query.start_date).getTime())) {
      errors.push('Invalid start_date format');
    }
    
    if (query.end_date && isNaN(new Date(query.end_date).getTime())) {
      errors.push('Invalid end_date format');
    }
    
    // 验证limit参数
    if (query.limit) {
      const limit = parseInt(query.limit);
      if (isNaN(limit) || limit <= 0 || limit > 100) {
        errors.push('Invalid limit: must be between 1 and 100');
      }
    }
    
    if (errors.length > 0) {
      throw new ValidationError('库存查询参数验证失败', errors);
    }
    
    await next();
  };
};

/**
 * 验证生产查询参数中间件
 */
export const validateProductionQuery = () => {
  return async (c: Context, next: Next) => {
    const query = c.req.query() as ProductionQuery;
    const errors: string[] = [];
    
    // 验证日期范围
    if (query.start_date && query.end_date) {
      const validation = DataValidator.validateDateRange(query.start_date, query.end_date);
      if (!validation.isValid) {
        errors.push(...validation.errors);
      }
    }
    
    // 验证period参数
    if (query.period) {
      const period = parseInt(query.period);
      if (isNaN(period) || period <= 0 || period > 365) {
        errors.push('Invalid period: must be between 1 and 365 days');
      }
    }
    
    if (errors.length > 0) {
      throw new ValidationError('生产查询参数验证失败', errors);
    }
    
    await next();
  };
};

/**
 * 验证价格查询参数中间件
 */
export const validatePricingQuery = () => {
  return async (c: Context, next: Next) => {
    const query = c.req.query() as PricingQuery;
    const errors: string[] = [];
    
    // 验证产品ID
    if (query.product_id) {
      const productId = parseInt(query.product_id);
      if (isNaN(productId) || productId <= 0) {
        errors.push('Invalid product_id: must be a positive integer');
      }
    }
    
    // 验证最小价格差异
    if (query.min_price_diff) {
      const minPriceDiff = parseFloat(query.min_price_diff);
      if (isNaN(minPriceDiff)) {
        errors.push('Invalid min_price_diff: must be a number');
      }
    }
    
    // 验证预警类型
    if (query.alert_type) {
      const validAlertTypes = ['daily_drop', 'consecutive_drop', 'volatility', 'threshold'];
      if (!validAlertTypes.includes(query.alert_type)) {
        errors.push(`Invalid alert_type: must be one of ${validAlertTypes.join(', ')}`);
      }
    }
    
    // 验证预警级别
    if (query.alert_level) {
      const validAlertLevels = ['low', 'medium', 'high', 'critical'];
      if (!validAlertLevels.includes(query.alert_level)) {
        errors.push(`Invalid alert_level: must be one of ${validAlertLevels.join(', ')}`);
      }
    }
    
    // 验证acknowledged参数
    if (query.acknowledged) {
      const acknowledged = query.acknowledged.toLowerCase();
      if (!['true', 'false', '1', '0'].includes(acknowledged)) {
        errors.push('Invalid acknowledged: must be true, false, 1, or 0');
      }
    }
    
    if (errors.length > 0) {
      throw new ValidationError('价格查询参数验证失败', errors);
    }
    
    await next();
  };
};

/**
 * 验证JSON请求体中间件
 */
export const validateJsonBody = (requiredFields: string[] = []) => {
  return async (c: Context, next: Next) => {
    try {
      const body = await c.req.json();
      const errors: string[] = [];
      
      // 检查必需字段
      requiredFields.forEach(field => {
        if (!(field in body) || body[field] === undefined || body[field] === null) {
          errors.push(`Missing required field: ${field}`);
        }
      });
      
      if (errors.length > 0) {
        throw new ValidationError('请求体验证失败', errors);
      }
      
      // 将验证后的body存储到context中
      c.set('validatedBody', body);
      
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError('请求体格式错误', ['Invalid JSON format']);
    }
    
    await next();
  };
};

/**
 * 验证文件上传中间件
 */
export const validateFileUpload = (allowedTypes: string[] = [], maxSize: number = 10 * 1024 * 1024) => {
  return async (c: Context, next: Next) => {
    try {
      const formData = await c.req.formData();
      const file = formData.get('file') as File;
      
      if (!file) {
        throw new ValidationError('文件上传失败', ['No file provided']);
      }
      
      const errors: string[] = [];
      
      // 验证文件类型
      if (allowedTypes.length > 0) {
        const fileType = file.type;
        const fileName = file.name;
        const fileExtension = fileName.split('.').pop()?.toLowerCase();
        
        const isValidType = allowedTypes.some(type => 
          fileType.includes(type) || 
          (fileExtension && type.includes(fileExtension))
        );
        
        if (!isValidType) {
          errors.push(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
        }
      }
      
      // 验证文件大小
      if (file.size > maxSize) {
        errors.push(`File too large. Maximum size: ${maxSize / (1024 * 1024)}MB`);
      }
      
      if (errors.length > 0) {
        throw new ValidationError('文件验证失败', errors);
      }
      
      // 将验证后的文件存储到context中
      c.set('validatedFile', file);
      
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError('文件处理失败', ['Failed to process uploaded file']);
    }
    
    await next();
  };
};

/**
 * 验证分页参数中间件
 */
export const validatePagination = () => {
  return async (c: Context, next: Next) => {
    const { page = '1', limit = '20' } = c.req.query();
    const errors: string[] = [];
    
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    
    if (isNaN(pageNum) || pageNum < 1) {
      errors.push('Invalid page: must be a positive integer');
    }
    
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      errors.push('Invalid limit: must be between 1 and 100');
    }
    
    if (errors.length > 0) {
      throw new ValidationError('分页参数验证失败', errors);
    }
    
    // 将验证后的分页参数存储到context中
    c.set('pagination', {
      page: pageNum,
      limit: limitNum,
      offset: (pageNum - 1) * limitNum
    });
    
    await next();
  };
};

/**
 * 通用参数验证中间件工厂
 */
export const createValidator = (validationRules: Record<string, (value: any) => boolean | string>) => {
  return async (c: Context, next: Next) => {
    const query = c.req.query();
    const errors: string[] = [];
    
    Object.entries(validationRules).forEach(([field, validator]) => {
      if (query[field] !== undefined) {
        const result = validator(query[field]);
        if (typeof result === 'string') {
          errors.push(`${field}: ${result}`);
        } else if (result === false) {
          errors.push(`Invalid ${field}`);
        }
      }
    });
    
    if (errors.length > 0) {
      throw new ValidationError('参数验证失败', errors);
    }
    
    await next();
  };
};
