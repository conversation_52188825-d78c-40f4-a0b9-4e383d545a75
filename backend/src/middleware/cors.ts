/**
 * CORS中间件配置
 * 提供统一的跨域资源共享配置
 */

import type { Context, Next } from 'hono';

/**
 * 允许的源地址列表
 */
export const ALLOWED_ORIGINS = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3003',
  'http://localhost:3004',
  'http://127.0.0.1:3000',
  'http://localhost:8000',
  'http://localhost:8080',
  'https://my-fullstack-project.pages.dev',
  'https://7bbead58.my-fullstack-project.pages.dev',
  'https://backend.qu18354531302.workers.dev',
  'https://my-auth-worker.qu18354531302.workers.dev'
];

/**
 * CORS响应头配置
 */
export const CORS_HEADERS = {
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Accept, Authorization, X-Requested-With',
  'Access-Control-Expose-Headers': 'Content-Length, X-Requested-With',
  'Access-Control-Max-Age': '86400',
};

/**
 * CORS配置选项
 */
export interface CorsOptions {
  origins?: string[];
  methods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
  optionsSuccessStatus?: number;
}

/**
 * 默认CORS配置
 */
export const DEFAULT_CORS_OPTIONS: CorsOptions = {
  origins: ALLOWED_ORIGINS,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Accept', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Length', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400,
  optionsSuccessStatus: 204
};

/**
 * 检查源地址是否被允许
 */
export const isOriginAllowed = (origin: string | undefined, allowedOrigins: string[]): boolean => {
  if (!origin) return false;
  
  return allowedOrigins.some(allowedOrigin => {
    // 精确匹配
    if (allowedOrigin === origin) return true;
    
    // 通配符匹配
    if (allowedOrigin.includes('*')) {
      const pattern = allowedOrigin.replace(/\*/g, '.*');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(origin);
    }
    
    return false;
  });
};

/**
 * 自定义CORS中间件
 */
export const corsMiddleware = (options: CorsOptions = {}) => {
  const config = { ...DEFAULT_CORS_OPTIONS, ...options };
  
  return async (c: Context, next: Next) => {
    const origin = c.req.header('Origin');
    const requestMethod = c.req.method;
    
    // 处理预检请求
    if (requestMethod === 'OPTIONS') {
      const requestedMethod = c.req.header('Access-Control-Request-Method');
      const requestedHeaders = c.req.header('Access-Control-Request-Headers');
      
      // 检查源地址
      if (origin && config.origins && isOriginAllowed(origin, config.origins)) {
        c.header('Access-Control-Allow-Origin', origin);
      }
      
      // 设置允许的方法
      if (requestedMethod && config.methods?.includes(requestedMethod)) {
        c.header('Access-Control-Allow-Methods', config.methods.join(', '));
      }
      
      // 设置允许的头部
      if (requestedHeaders && config.allowedHeaders) {
        const requestedHeadersList = requestedHeaders.split(',').map(h => h.trim());
        const allowedHeadersList = requestedHeadersList.filter(h => 
          config.allowedHeaders!.some(allowed => 
            allowed.toLowerCase() === h.toLowerCase()
          )
        );
        
        if (allowedHeadersList.length > 0) {
          c.header('Access-Control-Allow-Headers', allowedHeadersList.join(', '));
        }
      }
      
      // 设置其他CORS头部
      if (config.credentials) {
        c.header('Access-Control-Allow-Credentials', 'true');
      }
      
      if (config.maxAge) {
        c.header('Access-Control-Max-Age', config.maxAge.toString());
      }
      
      return c.text('', config.optionsSuccessStatus || 204);
    }
    
    // 处理实际请求
    if (origin && config.origins && isOriginAllowed(origin, config.origins)) {
      c.header('Access-Control-Allow-Origin', origin);
      
      if (config.credentials) {
        c.header('Access-Control-Allow-Credentials', 'true');
      }
      
      if (config.exposedHeaders && config.exposedHeaders.length > 0) {
        c.header('Access-Control-Expose-Headers', config.exposedHeaders.join(', '));
      }
    }
    
    await next();
  };
};

/**
 * 开发环境CORS中间件（允许所有源）
 */
export const devCorsMiddleware = () => {
  return async (c: Context, next: Next) => {
    const origin = c.req.header('Origin');
    
    if (c.req.method === 'OPTIONS') {
      c.header('Access-Control-Allow-Origin', origin || '*');
      c.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      c.header('Access-Control-Allow-Headers', 'Content-Type, Accept, Authorization, X-Requested-With');
      c.header('Access-Control-Allow-Credentials', 'true');
      c.header('Access-Control-Max-Age', '86400');
      return c.text('', 204);
    }
    
    c.header('Access-Control-Allow-Origin', origin || '*');
    c.header('Access-Control-Allow-Credentials', 'true');
    c.header('Access-Control-Expose-Headers', 'Content-Length, X-Requested-With');
    
    await next();
  };
};

/**
 * 生产环境CORS中间件（严格的源检查）
 */
export const prodCorsMiddleware = () => {
  return corsMiddleware({
    origins: ALLOWED_ORIGINS,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Accept', 'Authorization'],
    exposedHeaders: ['Content-Length']
  });
};

/**
 * 根据环境自动选择CORS中间件
 */
export const autoCorsMiddleware = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  return isDevelopment ? devCorsMiddleware() : prodCorsMiddleware();
};

/**
 * CORS工具函数
 */
export const CorsUtils = {
  /**
   * 检查请求是否为跨域请求
   */
  isCrossOrigin(c: Context): boolean {
    const origin = c.req.header('Origin');
    const host = c.req.header('Host');
    
    if (!origin || !host) return false;
    
    try {
      const originUrl = new URL(origin);
      return originUrl.host !== host;
    } catch {
      return false;
    }
  },
  
  /**
   * 获取请求的源地址
   */
  getOrigin(c: Context): string | undefined {
    return c.req.header('Origin') || c.req.header('Referer');
  },
  
  /**
   * 设置CORS头部
   */
  setCorsHeaders(c: Context, options: Partial<CorsOptions> = {}) {
    const origin = c.req.header('Origin');
    
    if (origin && options.origins && isOriginAllowed(origin, options.origins)) {
      c.header('Access-Control-Allow-Origin', origin);
    }
    
    if (options.credentials) {
      c.header('Access-Control-Allow-Credentials', 'true');
    }
    
    if (options.exposedHeaders) {
      c.header('Access-Control-Expose-Headers', options.exposedHeaders.join(', '));
    }
  },
  
  /**
   * 验证CORS预检请求
   */
  validatePreflightRequest(c: Context, options: CorsOptions): boolean {
    const origin = c.req.header('Origin');
    const method = c.req.header('Access-Control-Request-Method');
    const headers = c.req.header('Access-Control-Request-Headers');
    
    // 检查源地址
    if (!origin || !options.origins || !isOriginAllowed(origin, options.origins)) {
      return false;
    }
    
    // 检查方法
    if (!method || !options.methods?.includes(method)) {
      return false;
    }
    
    // 检查头部
    if (headers && options.allowedHeaders) {
      const requestedHeaders = headers.split(',').map(h => h.trim().toLowerCase());
      const allowedHeaders = options.allowedHeaders.map(h => h.toLowerCase());
      
      const hasDisallowedHeader = requestedHeaders.some(h => !allowedHeaders.includes(h));
      if (hasDisallowedHeader) {
        return false;
      }
    }
    
    return true;
  }
};
