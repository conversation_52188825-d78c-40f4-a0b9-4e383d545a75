/**
 * 错误处理中间件
 * 提供统一的错误处理和响应格式
 */

import type { Context } from 'hono';
import type { ApiResponse } from '../types/api';

/**
 * 自定义错误类
 */
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public details?: any;

  constructor(message: string, statusCode: number = 500, code: string = 'INTERNAL_ERROR', details?: any) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

/**
 * 业务错误类
 */
export class BusinessError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'BUSINESS_ERROR', details);
  }
}

/**
 * 验证错误类
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * 数据库错误类
 */
export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

/**
 * 认证错误类
 */
export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

/**
 * 授权错误类
 */
export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * 资源未找到错误类
 */
export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

/**
 * 全局错误处理中间件
 */
export const errorHandler = (err: Error, c: Context) => {
  console.error('API错误:', {
    name: err.name,
    message: err.message,
    stack: err.stack,
    url: c.req.url,
    method: c.req.method,
    timestamp: new Date().toISOString()
  });

  // 处理自定义错误
  if (err instanceof AppError) {
    const response: ApiResponse = {
      success: false,
      error: err.message,
      details: err.details
    };

    return c.json(response, err.statusCode);
  }

  // 处理数据库错误
  if (err.message.includes('D1_ERROR') || err.message.includes('database')) {
    const response: ApiResponse = {
      success: false,
      error: '数据库操作失败',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    };

    return c.json(response, 500);
  }

  // 处理JSON解析错误
  if (err instanceof SyntaxError && err.message.includes('JSON')) {
    const response: ApiResponse = {
      success: false,
      error: '请求数据格式错误',
      details: 'Invalid JSON format'
    };

    return c.json(response, 400);
  }

  // 默认错误处理
  const response: ApiResponse = {
    success: false,
    error: '服务器内部错误',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  };

  return c.json(response, 500);
};

/**
 * 异步错误包装器
 */
export const asyncHandler = (fn: Function) => {
  return async (c: Context, next?: Function) => {
    try {
      return await fn(c, next);
    } catch (error) {
      return errorHandler(error as Error, c);
    }
  };
};

/**
 * 错误响应构建器
 */
export const ErrorResponse = {
  /**
   * 创建成功响应
   */
  success<T>(data: T, message?: string): ApiResponse<T> {
    return {
      success: true,
      data,
      message
    };
  },

  /**
   * 创建错误响应
   */
  error(message: string, details?: any, statusCode: number = 500): { response: ApiResponse; statusCode: number } {
    return {
      response: {
        success: false,
        error: message,
        details
      },
      statusCode
    };
  },

  /**
   * 创建验证错误响应
   */
  validationError(errors: string[]): { response: ApiResponse; statusCode: number } {
    return {
      response: {
        success: false,
        error: '数据验证失败',
        details: errors
      },
      statusCode: 400
    };
  },

  /**
   * 创建业务错误响应
   */
  businessError(message: string, details?: any): { response: ApiResponse; statusCode: number } {
    return {
      response: {
        success: false,
        error: message,
        details
      },
      statusCode: 400
    };
  },

  /**
   * 创建数据库错误响应
   */
  databaseError(details?: any): { response: ApiResponse; statusCode: number } {
    return {
      response: {
        success: false,
        error: '数据库操作失败',
        details: process.env.NODE_ENV === 'development' ? details : undefined
      },
      statusCode: 500
    };
  },

  /**
   * 创建认证错误响应
   */
  authenticationError(): { response: ApiResponse; statusCode: number } {
    return {
      response: {
        success: false,
        error: '认证失败',
        details: 'Authentication required'
      },
      statusCode: 401
    };
  },

  /**
   * 创建授权错误响应
   */
  authorizationError(): { response: ApiResponse; statusCode: number } {
    return {
      response: {
        success: false,
        error: '权限不足',
        details: 'Insufficient permissions'
      },
      statusCode: 403
    };
  },

  /**
   * 创建资源未找到错误响应
   */
  notFoundError(resource: string = 'Resource'): { response: ApiResponse; statusCode: number } {
    return {
      response: {
        success: false,
        error: `${resource}未找到`,
        details: 'Resource not found'
      },
      statusCode: 404
    };
  }
};

/**
 * 错误日志记录器
 */
export const ErrorLogger = {
  /**
   * 记录错误日志
   */
  log(error: Error, context?: any) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context
    };

    console.error('Error Log:', JSON.stringify(logEntry, null, 2));
  },

  /**
   * 记录API错误
   */
  logApiError(error: Error, c: Context) {
    const context = {
      url: c.req.url,
      method: c.req.method,
      headers: Object.fromEntries(c.req.header()),
      userAgent: c.req.header('user-agent')
    };

    this.log(error, context);
  },

  /**
   * 记录数据库错误
   */
  logDatabaseError(error: Error, query?: string, params?: any[]) {
    const context = {
      type: 'database',
      query,
      params
    };

    this.log(error, context);
  }
};
