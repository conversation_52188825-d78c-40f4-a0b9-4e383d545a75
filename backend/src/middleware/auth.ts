/**
 * 认证中间件
 * 提供用户认证和授权功能
 */

import type { Context, Next } from 'hono';
import { AuthenticationError, AuthorizationError } from './errorHandler';

/**
 * 用户信息接口
 */
export interface User {
  id: number;
  username: string;
  role?: string;
  permissions?: string[];
}

/**
 * JWT载荷接口
 */
export interface JwtPayload {
  userId: number;
  username: string;
  role?: string;
  iat?: number;
  exp?: number;
}

/**
 * 认证配置
 */
export interface AuthConfig {
  secretKey: string;
  tokenExpiry: number; // 秒
  refreshTokenExpiry: number; // 秒
  issuer?: string;
  audience?: string;
}

/**
 * 默认认证配置
 */
export const DEFAULT_AUTH_CONFIG: AuthConfig = {
  secretKey: 'your-secret-key', // 应该从环境变量获取
  tokenExpiry: 3600, // 1小时
  refreshTokenExpiry: 604800, // 7天
  issuer: 'spring-snow-api',
  audience: 'spring-snow-frontend'
};

/**
 * 简单的JWT工具类（用于演示，生产环境建议使用专业库）
 */
export class JwtUtils {
  private static base64UrlEncode(str: string): string {
    return btoa(str)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  private static base64UrlDecode(str: string): string {
    str += '='.repeat((4 - str.length % 4) % 4);
    return atob(str.replace(/-/g, '+').replace(/_/g, '/'));
  }

  /**
   * 创建JWT令牌
   */
  static async createToken(payload: JwtPayload, config: AuthConfig): Promise<string> {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    };

    const now = Math.floor(Date.now() / 1000);
    const tokenPayload = {
      ...payload,
      iat: now,
      exp: now + config.tokenExpiry,
      iss: config.issuer,
      aud: config.audience
    };

    const encodedHeader = this.base64UrlEncode(JSON.stringify(header));
    const encodedPayload = this.base64UrlEncode(JSON.stringify(tokenPayload));
    
    const message = `${encodedHeader}.${encodedPayload}`;
    
    // 简化的HMAC-SHA256签名（生产环境应使用Web Crypto API）
    const signature = this.base64UrlEncode(await this.hmacSha256(message, config.secretKey));
    
    return `${message}.${signature}`;
  }

  /**
   * 验证JWT令牌
   */
  static async verifyToken(token: string, config: AuthConfig): Promise<JwtPayload> {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid token format');
    }

    const [encodedHeader, encodedPayload, signature] = parts;
    
    // 验证签名
    const message = `${encodedHeader}.${encodedPayload}`;
    const expectedSignature = this.base64UrlEncode(await this.hmacSha256(message, config.secretKey));
    
    if (signature !== expectedSignature) {
      throw new Error('Invalid token signature');
    }

    // 解析载荷
    const payload = JSON.parse(this.base64UrlDecode(encodedPayload)) as JwtPayload;
    
    // 验证过期时间
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      throw new Error('Token expired');
    }

    // 验证发行者和受众
    if (config.issuer && payload.iss !== config.issuer) {
      throw new Error('Invalid token issuer');
    }

    if (config.audience && payload.aud !== config.audience) {
      throw new Error('Invalid token audience');
    }

    return payload;
  }

  /**
   * 简化的HMAC-SHA256实现
   */
  private static async hmacSha256(message: string, key: string): Promise<string> {
    // 这是一个简化实现，生产环境应使用Web Crypto API
    // 这里仅用于演示目的
    return btoa(message + key); // 非常简化的实现
  }
}

/**
 * 认证中间件
 */
export const authMiddleware = (config: Partial<AuthConfig> = {}) => {
  const authConfig = { ...DEFAULT_AUTH_CONFIG, ...config };
  
  return async (c: Context, next: Next) => {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid authorization header');
    }
    
    const token = authHeader.substring(7); // 移除 "Bearer " 前缀
    
    try {
      const payload = await JwtUtils.verifyToken(token, authConfig);
      
      // 将用户信息存储到context中
      c.set('user', {
        id: payload.userId,
        username: payload.username,
        role: payload.role
      } as User);
      
      c.set('jwtPayload', payload);
      
    } catch (error) {
      throw new AuthenticationError('Invalid or expired token');
    }
    
    await next();
  };
};

/**
 * 可选认证中间件（不强制要求认证）
 */
export const optionalAuthMiddleware = (config: Partial<AuthConfig> = {}) => {
  const authConfig = { ...DEFAULT_AUTH_CONFIG, ...config };
  
  return async (c: Context, next: Next) => {
    const authHeader = c.req.header('Authorization');
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const payload = await JwtUtils.verifyToken(token, authConfig);
        
        c.set('user', {
          id: payload.userId,
          username: payload.username,
          role: payload.role
        } as User);
        
        c.set('jwtPayload', payload);
        c.set('authenticated', true);
        
      } catch (error) {
        // 忽略认证错误，继续处理请求
        c.set('authenticated', false);
      }
    } else {
      c.set('authenticated', false);
    }
    
    await next();
  };
};

/**
 * 角色授权中间件
 */
export const requireRole = (requiredRoles: string | string[]) => {
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  return async (c: Context, next: Next) => {
    const user = c.get('user') as User;
    
    if (!user) {
      throw new AuthenticationError('Authentication required');
    }
    
    if (!user.role || !roles.includes(user.role)) {
      throw new AuthorizationError(`Required role: ${roles.join(' or ')}`);
    }
    
    await next();
  };
};

/**
 * 权限授权中间件
 */
export const requirePermission = (requiredPermissions: string | string[]) => {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
  
  return async (c: Context, next: Next) => {
    const user = c.get('user') as User;
    
    if (!user) {
      throw new AuthenticationError('Authentication required');
    }
    
    if (!user.permissions) {
      throw new AuthorizationError('User has no permissions');
    }
    
    const hasPermission = permissions.some(permission => 
      user.permissions!.includes(permission)
    );
    
    if (!hasPermission) {
      throw new AuthorizationError(`Required permission: ${permissions.join(' or ')}`);
    }
    
    await next();
  };
};

/**
 * 用户所有权验证中间件
 */
export const requireOwnership = (resourceIdParam: string = 'id') => {
  return async (c: Context, next: Next) => {
    const user = c.get('user') as User;
    const resourceId = c.req.param(resourceIdParam);
    
    if (!user) {
      throw new AuthenticationError('Authentication required');
    }
    
    // 管理员可以访问所有资源
    if (user.role === 'admin') {
      await next();
      return;
    }
    
    // 检查资源所有权（这里需要根据具体业务逻辑实现）
    if (resourceId && parseInt(resourceId) !== user.id) {
      throw new AuthorizationError('Access denied: insufficient permissions');
    }
    
    await next();
  };
};

/**
 * 认证工具函数
 */
export const AuthUtils = {
  /**
   * 获取当前用户
   */
  getCurrentUser(c: Context): User | null {
    return c.get('user') || null;
  },
  
  /**
   * 检查用户是否已认证
   */
  isAuthenticated(c: Context): boolean {
    return !!c.get('user');
  },
  
  /**
   * 检查用户是否有指定角色
   */
  hasRole(c: Context, role: string): boolean {
    const user = c.get('user') as User;
    return user?.role === role;
  },
  
  /**
   * 检查用户是否有指定权限
   */
  hasPermission(c: Context, permission: string): boolean {
    const user = c.get('user') as User;
    return user?.permissions?.includes(permission) || false;
  },
  
  /**
   * 生成密码哈希
   */
  async hashPassword(password: string): Promise<string> {
    // 简化实现，生产环境应使用bcrypt或类似库
    const encoder = new TextEncoder();
    const data = encoder.encode(password + 'salt');
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  },
  
  /**
   * 验证密码
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    const passwordHash = await this.hashPassword(password);
    return passwordHash === hash;
  }
};
