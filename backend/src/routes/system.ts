/**
 * 系统相关路由
 * 处理系统管理和监控的API端点
 */

import { Hono } from 'hono';
import { DatabaseManager } from '../utils/database';
import { ProductionService } from '../services/productionService';
import { asyncHandler, ErrorResponse } from '../middleware/errorHandler';
import type { Bindings } from '../types/api';

const system = new Hono<{ Bindings: Bindings }>();

// 依赖注入中间件
system.use('*', async (c, next) => {
  c.set('db', new DatabaseManager(c.env.DB));
  c.set('productionService', new ProductionService(c.env.DB));
  await next();
});

/**
 * 健康检查
 * GET /api/system/health
 */
system.get('/health', asyncHandler(async (c) => {
  const db = c.get('db') as DatabaseManager;
  
  try {
    // 测试数据库连接
    await db.queryFirst('SELECT 1 as test');
    
    return c.json(ErrorResponse.success({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '2.0.0',
      database: 'connected',
      endpoints: {
        inventory_summary: '/api/inventory/summary',
        dashboard_summary: '/api/dashboard/summary',
        system_date_range: '/api/system/date-range'
      }
    }, '系统运行正常'));
    
  } catch (error) {
    const errorResponse = ErrorResponse.error('系统健康检查失败', error);
    return c.json(errorResponse.response, errorResponse.statusCode);
  }
}));

/**
 * 获取可用日期范围
 * GET /api/system/date-range
 */
system.get('/date-range', asyncHandler(async (c) => {
  const db = c.get('db') as DatabaseManager;
  
  console.log('🔄 从数据库获取可用日期范围...');
  
  const query = `
    SELECT 
      MIN(record_date) as min_date,
      MAX(record_date) as max_date,
      COUNT(DISTINCT record_date) as total_days,
      COUNT(*) as total_records
    FROM DailyMetrics
    WHERE record_date IS NOT NULL
  `;
  
  const result = await db.queryFirst<{
    min_date: string;
    max_date: string;
    total_days: number;
    total_records: number;
  }>(query);
  
  if (!result) {
    const errorResponse = ErrorResponse.error('未找到数据记录');
    return c.json(errorResponse.response, errorResponse.statusCode);
  }
  
  console.log('✅ 日期范围查询成功:', {
    min_date: result.min_date,
    max_date: result.max_date,
    total_days: result.total_days,
    total_records: result.total_records
  });
  
  return c.json(ErrorResponse.success({
    min_date: result.min_date,
    max_date: result.max_date,
    total_days: result.total_days,
    total_records: result.total_records,
    recommended_ranges: {
      last_7_days: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: new Date().toISOString().split('T')[0]
      },
      last_30_days: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        end: new Date().toISOString().split('T')[0]
      }
    }
  }, '日期范围获取成功'));
}));

/**
 * 获取产品列表
 * GET /api/system/products
 */
system.get('/products', asyncHandler(async (c) => {
  const db = c.get('db') as DatabaseManager;
  
  const result = await db.query('SELECT * FROM Products ORDER BY product_name');
  
  return c.json(ErrorResponse.success(result.results, '产品列表获取成功'));
}));

/**
 * 生产数据一致性监控
 * GET /api/system/monitoring/production-consistency
 */
system.get('/monitoring/production-consistency', asyncHandler(async (c) => {
  const productionService = c.get('productionService') as ProductionService;
  
  const currentDate = new Date().toISOString().split('T')[0];
  
  const consistency = await productionService.getProductionConsistency();
  
  console.log('📊 生产数据一致性监控:', consistency);
  
  const response = {
    monitoring_date: currentDate,
    consistency_metrics: consistency,
    status: consistency.data_quality_score >= 80 ? 'good' : 
            consistency.data_quality_score >= 60 ? 'warning' : 'critical',
    recommendations: []
  };
  
  // 添加建议
  if (consistency.production_coverage < 90) {
    response.recommendations.push('生产数据覆盖率偏低，建议检查数据导入流程');
  }
  
  if (consistency.sales_coverage < 90) {
    response.recommendations.push('销售数据覆盖率偏低，建议检查数据导入流程');
  }
  
  if (consistency.data_quality_score < 80) {
    response.recommendations.push('数据质量评分偏低，建议进行数据清理和验证');
  }
  
  return c.json(ErrorResponse.success(response, '生产数据一致性监控完成'));
}));

/**
 * 系统统计信息
 * GET /api/system/stats
 */
system.get('/stats', asyncHandler(async (c) => {
  const db = c.get('db') as DatabaseManager;
  
  const [
    dailyMetricsStats,
    productsStats,
    priceAdjustmentsStats,
    usersStats
  ] = await Promise.all([
    db.queryFirst(`
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT product_id) as unique_products,
        COUNT(DISTINCT record_date) as unique_dates,
        MIN(record_date) as earliest_date,
        MAX(record_date) as latest_date
      FROM DailyMetrics
    `),
    db.queryFirst('SELECT COUNT(*) as total_products FROM Products'),
    db.queryFirst(`
      SELECT 
        COUNT(*) as total_adjustments,
        MIN(adjustment_date) as earliest_adjustment,
        MAX(adjustment_date) as latest_adjustment
      FROM PriceAdjustments
    `),
    db.queryFirst('SELECT COUNT(*) as total_users FROM Users')
  ]);
  
  const stats = {
    daily_metrics: dailyMetricsStats,
    products: productsStats,
    price_adjustments: priceAdjustmentsStats,
    users: usersStats,
    system_info: {
      version: '2.0.0',
      environment: process.env.NODE_ENV || 'production',
      timestamp: new Date().toISOString()
    }
  };
  
  return c.json(ErrorResponse.success(stats, '系统统计信息获取成功'));
}));

export default system;
