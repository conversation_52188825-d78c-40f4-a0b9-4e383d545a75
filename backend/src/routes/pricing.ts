/**
 * 价格相关路由
 * 处理价格监控的API端点
 */

import { Hono } from 'hono';
import { PricingService } from '../services/pricingService';
import { validatePricingQuery, validateDateRange } from '../middleware/validation';
import { asyncHandler, ErrorResponse } from '../middleware/errorHandler';
import type { Bindings } from '../types/api';

const pricing = new Hono<{ Bindings: Bindings }>();

// 依赖注入中间件
pricing.use('*', async (c, next) => {
  c.set('pricingService', new PricingService(c.env.DB));
  await next();
});

/**
 * 获取价格变动记录
 * GET /api/pricing/changes
 */
pricing.get('/changes', validateDateRange(true), asyncHandler(async (c) => {
  const pricingService = c.get('pricingService') as PricingService;
  const query = c.req.query();
  
  const changes = await pricingService.getPriceChanges(query);
  
  return c.json(ErrorResponse.success(changes, '价格变动记录获取成功'));
}));

/**
 * 获取价格趋势数据
 * GET /api/pricing/trends
 */
pricing.get('/trends', validatePricingQuery(), asyncHandler(async (c) => {
  const pricingService = c.get('pricingService') as PricingService;
  const query = c.req.query();
  
  const [trends, summary] = await Promise.all([
    pricingService.getPriceTrends(query),
    pricingService.getPriceTrendsSummary(query)
  ]);
  
  const response = {
    trends,
    summary,
    total_records: trends.length
  };
  
  return c.json(ErrorResponse.success(response, '价格趋势数据获取成功'));
}));

/**
 * 获取价格预警
 * GET /api/pricing/alerts
 */
pricing.get('/alerts', validatePricingQuery(), asyncHandler(async (c) => {
  const pricingService = c.get('pricingService') as PricingService;
  const query = c.req.query();
  
  const alerts = await pricingService.getPriceAlerts(query);
  
  return c.json(ErrorResponse.success(alerts, '价格预警获取成功'));
}));

/**
 * 获取价格变动排名
 * GET /api/pricing/rankings
 */
pricing.get('/rankings', validateDateRange(true), asyncHandler(async (c) => {
  const pricingService = c.get('pricingService') as PricingService;
  const query = c.req.query();
  
  const rankings = await pricingService.getPriceRankings(query);
  
  return c.json(ErrorResponse.success(rankings, '价格变动排名获取成功'));
}));

/**
 * 确认价格预警
 * POST /api/pricing/alerts/:alertId/acknowledge
 */
pricing.post('/alerts/:alertId/acknowledge', asyncHandler(async (c) => {
  const pricingService = c.get('pricingService') as PricingService;
  const alertId = c.req.param('alertId');
  
  const success = await pricingService.acknowledgeAlert(alertId);
  
  if (success) {
    return c.json(ErrorResponse.success({ acknowledged: true }, '预警确认成功'));
  } else {
    const errorResponse = ErrorResponse.error('预警确认失败');
    return c.json(errorResponse.response, errorResponse.statusCode);
  }
}));

export default pricing;
