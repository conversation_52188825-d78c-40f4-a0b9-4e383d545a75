/**
 * 销售相关路由
 * 处理销售管理的API端点
 */

import { Hono } from 'hono';
import { SalesService } from '../services/salesService';
import { validateDateRange } from '../middleware/validation';
import { asyncHandler, ErrorResponse } from '../middleware/errorHandler';
import type { Bindings } from '../types/api';

const sales = new Hono<{ Bindings: Bindings }>();

// 依赖注入中间件
sales.use('*', async (c, next) => {
  c.set('salesService', new SalesService(c.env.DB));
  await next();
});

/**
 * 获取销售详情数据
 * GET /api/sales
 */
sales.get('/', validateDateRange(true), asyncHandler(async (c) => {
  const salesService = c.get('salesService') as SalesService;
  const { start_date, end_date } = c.req.query();
  
  const salesData = await salesService.getSalesData(start_date!, end_date!);
  
  return c.json(ErrorResponse.success(salesData, '销售数据获取成功'));
}));

/**
 * 获取销售汇总统计
 * GET /api/sales/summary
 */
sales.get('/summary', validateDateRange(true), asyncHandler(async (c) => {
  const salesService = c.get('salesService') as SalesService;
  const { start_date, end_date } = c.req.query();
  
  const summary = await salesService.getSalesSummary(start_date!, end_date!);
  
  return c.json(ErrorResponse.success(summary, '销售汇总统计获取成功'));
}));

/**
 * 获取产品销售排名
 * GET /api/sales/ranking
 */
sales.get('/ranking', validateDateRange(true), asyncHandler(async (c) => {
  const salesService = c.get('salesService') as SalesService;
  const { start_date, end_date, limit = '20' } = c.req.query();
  
  const ranking = await salesService.getProductSalesRanking(
    start_date!, 
    end_date!, 
    parseInt(limit)
  );
  
  return c.json(ErrorResponse.success(ranking, '产品销售排名获取成功'));
}));

/**
 * 获取销售趋势分析
 * GET /api/sales/trend-analysis
 */
sales.get('/trend-analysis', validateDateRange(true), asyncHandler(async (c) => {
  const salesService = c.get('salesService') as SalesService;
  const { start_date, end_date } = c.req.query();
  
  const analysis = await salesService.getSalesTrendAnalysis(start_date!, end_date!);
  
  return c.json(ErrorResponse.success(analysis, '销售趋势分析获取成功'));
}));

export default sales;
