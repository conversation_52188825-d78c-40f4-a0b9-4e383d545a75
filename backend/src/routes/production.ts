/**
 * 生产相关路由
 * 处理生产管理的API端点
 */

import { Hono } from 'hono';
import { ProductionService } from '../services/productionService';
import { validateProductionQuery, validateDateRange } from '../middleware/validation';
import { asyncHandler, ErrorResponse } from '../middleware/errorHandler';
import type { Bindings } from '../types/api';

const production = new Hono<{ Bindings: Bindings }>();

// 依赖注入中间件
production.use('*', async (c, next) => {
  c.set('productionService', new ProductionService(c.env.DB));
  await next();
});

/**
 * 获取产销比统计信息
 * GET /api/production/ratio-stats
 */
production.get('/ratio-stats', validateDateRange(true), asyncHandler(async (c) => {
  const productionService = c.get('productionService') as ProductionService;
  const { start_date, end_date } = c.req.query();
  
  const [statistics, consistencyCheck] = await Promise.all([
    productionService.calculateStatistics(start_date!, end_date!),
    productionService.validateConsistency(start_date!, end_date!)
  ]);
  
  // 记录一致性检查结果
  if (!consistencyCheck.isConsistent) {
    console.warn(`⚠️ [API] 产销比数据一致性检测到差异:
      - 聚合比率: ${consistencyCheck.aggregateRatio.toFixed(2)}%
      - 每日平均比率: ${consistencyCheck.dailyAverageRatio.toFixed(2)}%
      - 差异: ${consistencyCheck.difference.toFixed(2)}%`);
  }
  
  const response = {
    ...statistics,
    consistency_check: consistencyCheck,
    data_quality: {
      is_consistent: consistencyCheck.isConsistent,
      difference: consistencyCheck.difference,
      tolerance: consistencyCheck.tolerance
    }
  };
  
  return c.json(ErrorResponse.success(response, '产销比统计获取成功'));
}));

/**
 * 验证产销比计算一致性
 * GET /api/production/validate-consistency
 */
production.get('/validate-consistency', validateDateRange(true), asyncHandler(async (c) => {
  const productionService = c.get('productionService') as ProductionService;
  const { start_date, end_date } = c.req.query();
  
  const consistencyCheck = await productionService.validateConsistency(start_date!, end_date!);
  
  // 同时验证端点间的一致性
  const [ratioStats, trendsData] = await Promise.all([
    productionService.calculateStatistics(start_date!, end_date!),
    productionService.calculateDailyRatios(start_date!, end_date!)
  ]);
  
  const endpointConsistency = {
    ratio_stats_avg: ratioStats.avg_ratio,
    trends_avg: ratioStats.avg_ratio, // 两者现在使用相同的计算器
    endpoints_consistent: true
  };
  
  console.log(`🔍 [API] 一致性验证 ${start_date} 到 ${end_date}:`, {
    ...consistencyCheck,
    ...endpointConsistency
  });
  
  const response = {
    calculation_consistency: consistencyCheck,
    endpoint_consistency: endpointConsistency,
    validation_timestamp: new Date().toISOString(),
    date_range: { start_date, end_date }
  };
  
  return c.json(ErrorResponse.success(response, '一致性验证完成'));
}));

export default production;
