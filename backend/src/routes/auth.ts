/**
 * 认证相关路由
 * 处理用户认证的API端点
 */

import { Hono } from 'hono';
import { DatabaseManager } from '../utils/database';
import { validateJsonBody } from '../middleware/validation';
import { asyncHandler, ErrorResponse, ValidationError } from '../middleware/errorHandler';
import { AuthUtils, JwtUtils, DEFAULT_AUTH_CONFIG } from '../middleware/auth';
import type { Bindings } from '../types/api';
import type { UserRow } from '../types/database';

const auth = new Hono<{ Bindings: Bindings }>();

// 依赖注入中间件
auth.use('*', async (c, next) => {
  c.set('db', new DatabaseManager(c.env.DB));
  await next();
});

/**
 * 用户注册
 * POST /api/auth/register
 */
auth.post('/register', validateJsonBody(['username', 'password', 'inviteCode']), asyncHandler(async (c) => {
  const db = c.get('db') as DatabaseManager;
  const { username, password, inviteCode } = c.get('validatedBody');
  
  // 验证邀请码
  if (inviteCode !== 'SPRING_SNOW_2024') {
    throw new ValidationError('无效的邀请码');
  }
  
  // 检查用户名是否已存在
  const existingUser = await db.queryFirst<UserRow>(
    'SELECT id FROM Users WHERE username = ?',
    [username]
  );
  
  if (existingUser) {
    throw new ValidationError('用户名已存在');
  }
  
  // 创建用户
  const passwordHash = await AuthUtils.hashPassword(password);
  
  const result = await db.query(
    'INSERT INTO Users (username, password_hash) VALUES (?, ?)',
    [username, passwordHash]
  );
  
  if (!result.success) {
    const errorResponse = ErrorResponse.error('用户注册失败');
    return c.json(errorResponse.response, errorResponse.statusCode);
  }
  
  return c.json(ErrorResponse.success(
    { message: '用户注册成功', username },
    '注册成功'
  ));
}));

/**
 * 用户登录
 * POST /api/auth/login
 */
auth.post('/login', validateJsonBody(['username', 'password']), asyncHandler(async (c) => {
  const db = c.get('db') as DatabaseManager;
  const { username, password } = c.get('validatedBody');
  
  // 查找用户
  const user = await db.queryFirst<UserRow>(
    'SELECT id, username, password_hash FROM Users WHERE username = ?',
    [username]
  );
  
  if (!user) {
    throw new ValidationError('用户名或密码错误');
  }
  
  // 验证密码
  const isValidPassword = await AuthUtils.verifyPassword(password, user.password_hash);
  
  if (!isValidPassword) {
    throw new ValidationError('用户名或密码错误');
  }
  
  // 生成JWT令牌
  const token = await JwtUtils.createToken(
    {
      userId: user.id!,
      username: user.username,
      role: 'user' // 默认角色
    },
    DEFAULT_AUTH_CONFIG
  );
  
  return c.json(ErrorResponse.success(
    {
      token,
      user: {
        id: user.id,
        username: user.username,
        role: 'user'
      }
    },
    '登录成功'
  ));
}));

/**
 * 刷新令牌
 * POST /api/auth/refresh
 */
auth.post('/refresh', validateJsonBody(['token']), asyncHandler(async (c) => {
  const { token } = c.get('validatedBody');
  
  try {
    // 验证现有令牌
    const payload = await JwtUtils.verifyToken(token, DEFAULT_AUTH_CONFIG);
    
    // 生成新令牌
    const newToken = await JwtUtils.createToken(
      {
        userId: payload.userId,
        username: payload.username,
        role: payload.role
      },
      DEFAULT_AUTH_CONFIG
    );
    
    return c.json(ErrorResponse.success(
      { token: newToken },
      '令牌刷新成功'
    ));
    
  } catch (error) {
    throw new ValidationError('无效的令牌');
  }
}));

/**
 * 获取用户信息
 * GET /api/auth/profile
 */
auth.get('/profile', asyncHandler(async (c) => {
  const db = c.get('db') as DatabaseManager;
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    const errorResponse = ErrorResponse.authenticationError();
    return c.json(errorResponse.response, errorResponse.statusCode);
  }
  
  const token = authHeader.substring(7);
  
  try {
    const payload = await JwtUtils.verifyToken(token, DEFAULT_AUTH_CONFIG);
    
    const user = await db.queryFirst<UserRow>(
      'SELECT id, username FROM Users WHERE id = ?',
      [payload.userId]
    );
    
    if (!user) {
      const errorResponse = ErrorResponse.notFoundError('用户');
      return c.json(errorResponse.response, errorResponse.statusCode);
    }
    
    return c.json(ErrorResponse.success(
      {
        id: user.id,
        username: user.username,
        role: payload.role || 'user'
      },
      '用户信息获取成功'
    ));
    
  } catch (error) {
    const errorResponse = ErrorResponse.authenticationError();
    return c.json(errorResponse.response, errorResponse.statusCode);
  }
}));

export default auth;
