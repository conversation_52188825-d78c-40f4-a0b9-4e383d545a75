/**
 * 库存相关路由
 * 处理库存管理的API端点
 */

import { Hono } from 'hono';
import { InventoryService } from '../services/inventoryService';
import { validateInventoryQuery, validateDateRange } from '../middleware/validation';
import { asyncHandler, ErrorResponse } from '../middleware/errorHandler';
import type { Bindings } from '../types/api';

const inventory = new Hono<{ Bindings: Bindings }>();

// 依赖注入中间件
inventory.use('*', async (c, next) => {
  c.set('inventoryService', new InventoryService(c.env.DB));
  await next();
});

/**
 * 获取库存汇总信息
 * GET /api/inventory/summary
 */
inventory.get('/summary', validateInventoryQuery(), asyncHandler(async (c) => {
  const inventoryService = c.get('inventoryService') as InventoryService;
  const query = c.req.query();
  
  const summary = await inventoryService.getSummary(query);
  
  return c.json(ErrorResponse.success(summary, '库存汇总获取成功'));
}));

/**
 * 获取未过滤的总库存汇总
 * GET /api/inventory/total-summary
 */
inventory.get('/total-summary', asyncHandler(async (c) => {
  const inventoryService = c.get('inventoryService') as InventoryService;
  const { date, end_date } = c.req.query();
  
  const summary = await inventoryService.getTotalSummary({ date, end_date });
  
  return c.json(ErrorResponse.success(summary, '总库存汇总获取成功'));
}));

/**
 * 获取TOP N库存产品
 * GET /api/inventory/top
 */
inventory.get('/top', asyncHandler(async (c) => {
  const inventoryService = c.get('inventoryService') as InventoryService;
  const { date, limit = '15', end_date } = c.req.query();
  
  const topProducts = await inventoryService.getTopProducts({ date, limit, end_date });
  
  return c.json(ErrorResponse.success(topProducts, 'TOP库存产品获取成功'));
}));

/**
 * 获取库存分布数据（饼图）
 * GET /api/inventory/distribution
 */
inventory.get('/distribution', asyncHandler(async (c) => {
  const inventoryService = c.get('inventoryService') as InventoryService;
  const { date, limit = '15' } = c.req.query();
  
  const distribution = await inventoryService.getDistribution({ date, limit });
  
  return c.json(ErrorResponse.success(distribution, '库存分布数据获取成功'));
}));

/**
 * 获取库存趋势数据
 * GET /api/inventory/trend
 */
inventory.get('/trend', validateDateRange(true), asyncHandler(async (c) => {
  const inventoryService = c.get('inventoryService') as InventoryService;
  const { start_date, end_date } = c.req.query();
  
  const trends = await inventoryService.getTrends({ start_date, end_date });
  
  return c.json(ErrorResponse.success(trends, '库存趋势数据获取成功'));
}));

/**
 * 获取库存趋势数据（包含周转天数）
 * GET /api/inventory/trends
 */
inventory.get('/trends', validateDateRange(true), asyncHandler(async (c) => {
  const inventoryService = c.get('inventoryService') as InventoryService;
  const { start_date, end_date, product_id } = c.req.query();
  
  const trends = await inventoryService.getTrends({ start_date, end_date, product_id });
  
  return c.json(ErrorResponse.success(trends, '库存趋势数据获取成功'));
}));

export default inventory;
