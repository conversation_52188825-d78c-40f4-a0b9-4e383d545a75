/**
 * 趋势分析相关路由
 * 处理各种趋势分析的API端点
 */

import { Hono } from 'hono';
import { ProductionService } from '../services/productionService';
import { SalesService } from '../services/salesService';
import { validateDateRange } from '../middleware/validation';
import { asyncHandler, ErrorResponse } from '../middleware/errorHandler';
import type { Bindings } from '../types/api';
import type { Context } from 'hono';

const trends = new Hono<{
  Bindings: Bindings;
  Variables: {
    productionService: ProductionService;
    salesService: SalesService;
  };
}>();

// 依赖注入中间件
trends.use('*', async (c, next) => {
  c.set('productionService', new ProductionService(c.env.DB));
  c.set('salesService', new SalesService(c.env.DB));
  await next();
});

/**
 * 获取产销比趋势数据
 * GET /api/trends/ratio
 */
trends.get('/ratio', validateDateRange(true), asyncHandler(async (c: Context) => {
  const productionService = c.get('productionService') as ProductionService;
  const { start_date, end_date } = c.req.query();
  
  const [dailyRatios, avgRatio, consistencyCheck] = await Promise.all([
    productionService.calculateDailyRatios(start_date!, end_date!),
    productionService.calculateAggregateRatio(start_date!, end_date!),
    productionService.validateConsistency(start_date!, end_date!)
  ]);
  
  console.log(`📈 [API] 产销比趋势 ${start_date} 到 ${end_date}:`, {
    daily_points: dailyRatios.length,
    avg_ratio: avgRatio,
    is_consistent: consistencyCheck.isConsistent
  });
  
  // 修复响应格式以匹配原始API格式，确保前端兼容性
  const response = {
    daily_data: dailyRatios.map(item => ({
      record_date: item.date,
      daily_sales: item.sales_volume, // 保持千克单位，与数据库一致
      daily_production: item.production_volume, // 保持千克单位，与数据库一致
      ratio: item.ratio,
      calculation_method: 'Unified Calculator'
    })),
    avg_ratio: avgRatio,
    total_days: dailyRatios.length,
    calculation_method: 'Unified with ratio-stats',
    consistency_check: {
      is_consistent: consistencyCheck.isConsistent,
      difference: consistencyCheck.difference
    }
  };
  
  // 直接返回数据格式，与原始API保持一致
  return c.json(response);
}));

/**
 * 获取销售价格趋势数据
 * GET /api/trends/sales-price
 */
trends.get('/sales-price', validateDateRange(true), asyncHandler(async (c: Context) => {
  const salesService = c.get('salesService') as SalesService;
  const { start_date, end_date } = c.req.query();
  
  const trends = await salesService.getSalesPriceTrends(start_date!, end_date!);
  
  console.log(`📈 [API] 销售价格趋势 ${start_date} 到 ${end_date}:`, {
    data_points: trends.length,
    date_range: { start_date, end_date }
  });
  
  const response = {
    trends,
    period_summary: {
      start_date,
      end_date,
      total_days: trends.length
    }
  };
  
  return c.json(ErrorResponse.success(response, '销售价格趋势数据获取成功'));
}));

export default trends;
