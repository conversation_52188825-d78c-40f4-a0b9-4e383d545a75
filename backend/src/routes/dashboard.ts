/**
 * 仪表板相关路由
 * 处理仪表板汇总数据的API端点
 */

import { Hono } from 'hono';
import { DashboardService } from '../services/dashboardService';
import { validateDateRange } from '../middleware/validation';
import { asyncHandler, ErrorResponse } from '../middleware/errorHandler';
import type { Bindings } from '../types/api';

const dashboard = new Hono<{ Bindings: Bindings }>();

// 依赖注入中间件
dashboard.use('*', async (c, next) => {
  c.set('dashboardService', new DashboardService(c.env.DB));
  await next();
});

/**
 * 获取仪表板汇总数据
 * GET /api/dashboard/summary
 */
dashboard.get('/summary', validateDateRange(true), asyncHandler(async (c) => {
  const dashboardService = c.get('dashboardService') as DashboardService;
  const { start_date, end_date } = c.req.query();
  
  const summary = await dashboardService.getSummary(start_date!, end_date!);
  
  console.log(`📊 [API] 仪表板汇总数据 ${start_date} 到 ${end_date}:`, {
    total_inventory: summary.total_inventory,
    total_sales: summary.total_sales,
    total_production: summary.total_production,
    avg_production_ratio: summary.avg_production_ratio
  });
  
  return c.json(ErrorResponse.success(summary, '仪表板汇总数据获取成功'));
}));

/**
 * 获取关键绩效指标（KPI）
 * GET /api/dashboard/kpis
 */
dashboard.get('/kpis', validateDateRange(true), asyncHandler(async (c) => {
  const dashboardService = c.get('dashboardService') as DashboardService;
  const { start_date, end_date } = c.req.query();
  
  const kpis = await dashboardService.getKPIs(start_date!, end_date!);
  
  return c.json(ErrorResponse.success(kpis, 'KPI指标获取成功'));
}));

export default dashboard;
