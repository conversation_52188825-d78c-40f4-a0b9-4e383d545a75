/**
 * 数据验证器工具
 * 提供统一的数据验证功能
 */

import type { ValidationResult } from '../types/api';
import type { DailyMetricsRow, ProductRow, PriceAdjustmentRow } from '../types/database';

/**
 * 通用验证器类
 */
export class DataValidator {
  /**
   * 验证DailyMetrics行数据
   */
  static validateDailyMetricsRow(row: any): ValidationResult {
    const errors: string[] = [];

    // 必需字段验证
    if (!row.product_id || isNaN(parseInt(row.product_id))) {
      errors.push('Invalid or missing product_id');
    }

    if (!row.record_date) {
      errors.push('Missing record_date');
    } else {
      // 验证日期格式
      const date = new Date(row.record_date);
      if (isNaN(date.getTime())) {
        errors.push('Invalid record_date format');
      }
    }

    // 数值字段验证
    const numericFields = ['production_volume', 'sales_volume', 'inventory_level', 'average_price', 'sales_amount'];
    numericFields.forEach(field => {
      if (row[field] !== undefined && row[field] !== null) {
        const value = parseFloat(row[field]);
        if (isNaN(value)) {
          errors.push(`Invalid ${field}: must be a number`);
        } else if (value < 0) {
          errors.push(`Invalid ${field}: must be non-negative`);
        }
      }
    });

    // 业务逻辑验证
    if (row.sales_volume && row.sales_amount) {
      const salesVolume = parseFloat(row.sales_volume);
      const salesAmount = parseFloat(row.sales_amount);
      if (salesVolume > 0 && salesAmount <= 0) {
        errors.push('Sales amount must be positive when sales volume is positive');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证产品数据
   */
  static validateProductRow(row: any): ValidationResult {
    const errors: string[] = [];

    // 必需字段验证
    if (!row.product_id || isNaN(parseInt(row.product_id))) {
      errors.push('Invalid or missing product_id');
    }

    if (!row.product_name || typeof row.product_name !== 'string' || row.product_name.trim().length === 0) {
      errors.push('Invalid or missing product_name');
    }

    // 产品名称长度验证
    if (row.product_name && row.product_name.length > 255) {
      errors.push('Product name too long (max 255 characters)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证价格调整数据
   */
  static validatePriceAdjustmentRow(row: any): ValidationResult {
    const errors: string[] = [];

    // 必需字段验证
    if (!row.product_id || isNaN(parseInt(row.product_id))) {
      errors.push('Invalid or missing product_id');
    }

    if (!row.product_name || typeof row.product_name !== 'string') {
      errors.push('Invalid or missing product_name');
    }

    if (!row.adjustment_date) {
      errors.push('Missing adjustment_date');
    } else {
      const date = new Date(row.adjustment_date);
      if (isNaN(date.getTime())) {
        errors.push('Invalid adjustment_date format');
      }
    }

    // 价格字段验证
    const priceFields = ['previous_price', 'current_price'];
    priceFields.forEach(field => {
      if (row[field] === undefined || row[field] === null) {
        errors.push(`Missing ${field}`);
      } else {
        const value = parseFloat(row[field]);
        if (isNaN(value) || value <= 0) {
          errors.push(`Invalid ${field}: must be a positive number`);
        }
      }
    });

    // 价格差异验证
    if (row.previous_price && row.current_price) {
      const prevPrice = parseFloat(row.previous_price);
      const currPrice = parseFloat(row.current_price);
      const expectedDiff = currPrice - prevPrice;
      
      if (row.price_difference !== undefined) {
        const actualDiff = parseFloat(row.price_difference);
        if (Math.abs(actualDiff - expectedDiff) > 0.01) {
          errors.push('Price difference does not match calculated value');
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证日期范围
   */
  static validateDateRange(startDate: string, endDate: string): ValidationResult {
    const errors: string[] = [];

    // 日期格式验证
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    if (isNaN(startDateObj.getTime())) {
      errors.push('Invalid start_date format');
    }

    if (isNaN(endDateObj.getTime())) {
      errors.push('Invalid end_date format');
    }

    // 日期逻辑验证
    if (!isNaN(startDateObj.getTime()) && !isNaN(endDateObj.getTime())) {
      if (startDateObj > endDateObj) {
        errors.push('start_date must be before or equal to end_date');
      }

      // 检查日期范围是否过大（例如超过1年）
      const daysDiff = (endDateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        errors.push('Date range too large (maximum 365 days)');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证查询参数
   */
  static validateQueryParams(params: Record<string, any>, requiredFields: string[] = []): ValidationResult {
    const errors: string[] = [];

    // 检查必需字段
    requiredFields.forEach(field => {
      if (!params[field]) {
        errors.push(`Missing required parameter: ${field}`);
      }
    });

    // 验证数值参数
    const numericParams = ['limit', 'page', 'product_id', 'min_price_diff'];
    numericParams.forEach(param => {
      if (params[param] !== undefined) {
        const value = parseInt(params[param]);
        if (isNaN(value) || value < 0) {
          errors.push(`Invalid ${param}: must be a non-negative integer`);
        }
      }
    });

    // 验证日期参数
    const dateParams = ['start_date', 'end_date', 'date', 'adjustment_date'];
    dateParams.forEach(param => {
      if (params[param] !== undefined) {
        const date = new Date(params[param]);
        if (isNaN(date.getTime())) {
          errors.push(`Invalid ${param}: must be a valid date`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证Excel上传数据
   */
  static validateExcelData(data: any[]): ValidationResult {
    const errors: string[] = [];

    if (!Array.isArray(data)) {
      errors.push('Data must be an array');
      return { isValid: false, errors };
    }

    if (data.length === 0) {
      errors.push('Data array is empty');
      return { isValid: false, errors };
    }

    // 验证数据结构一致性
    const firstRow = data[0];
    const expectedKeys = Object.keys(firstRow);

    data.forEach((row, index) => {
      const rowKeys = Object.keys(row);
      if (rowKeys.length !== expectedKeys.length) {
        errors.push(`Row ${index + 1}: Inconsistent number of columns`);
      }

      expectedKeys.forEach(key => {
        if (!(key in row)) {
          errors.push(`Row ${index + 1}: Missing column '${key}'`);
        }
      });
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证批量数据
   */
  static validateBatchData<T>(
    data: any[],
    validator: (row: any) => ValidationResult,
    maxBatchSize: number = 1000
  ): { validRows: T[]; errors: string[]; totalErrors: number } {
    const validRows: T[] = [];
    const errors: string[] = [];
    let totalErrors = 0;

    if (data.length > maxBatchSize) {
      errors.push(`Batch size too large (max ${maxBatchSize} rows)`);
      return { validRows, errors, totalErrors };
    }

    data.forEach((row, index) => {
      const validation = validator(row);
      if (validation.isValid) {
        validRows.push(row as T);
      } else {
        totalErrors++;
        validation.errors.forEach(error => {
          errors.push(`Row ${index + 1}: ${error}`);
        });
      }
    });

    return { validRows, errors, totalErrors };
  }
}

/**
 * 业务规则验证器
 */
export class BusinessValidator {
  /**
   * 验证产销比数据的合理性
   */
  static validateProductionRatio(salesVolume: number, productionVolume: number): ValidationResult {
    const errors: string[] = [];

    if (salesVolume < 0 || productionVolume < 0) {
      errors.push('Sales and production volumes must be non-negative');
    }

    if (productionVolume === 0 && salesVolume > 0) {
      errors.push('Cannot have sales without production');
    }

    const ratio = productionVolume > 0 ? (salesVolume / productionVolume) * 100 : 0;
    if (ratio > 200) {
      errors.push('Production ratio seems unusually high (>200%)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证价格变动的合理性
   */
  static validatePriceChange(previousPrice: number, currentPrice: number): ValidationResult {
    const errors: string[] = [];

    if (previousPrice <= 0 || currentPrice <= 0) {
      errors.push('Prices must be positive');
    }

    const changePercentage = Math.abs((currentPrice - previousPrice) / previousPrice) * 100;
    if (changePercentage > 50) {
      errors.push('Price change seems unusually large (>50%)');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证库存数据的合理性
   */
  static validateInventoryData(inventoryLevel: number, salesVolume: number): ValidationResult {
    const errors: string[] = [];

    if (inventoryLevel < 0) {
      errors.push('Inventory level cannot be negative');
    }

    if (salesVolume > inventoryLevel * 10) {
      errors.push('Sales volume seems too high compared to inventory level');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
