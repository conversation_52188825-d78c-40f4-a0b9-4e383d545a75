/**
 * 数据过滤器工具
 * 基于原始Python脚本的产品过滤逻辑
 */

import type { FilterOptions } from '../types/database';

/**
 * 产品过滤器类
 * 实现与原始Python脚本相同的过滤逻辑
 */
export class ProductFilter {
  /**
   * 生成库存数据的SQL WHERE子句（严格过滤 - 排除鲜品）
   * 仅用于库存数据
   */
  static getInventoryFilter(tableAlias: string = 'p'): string {
    return `(
      -- 排除鲜品，但保留凤肠产品
      (${tableAlias}.product_name NOT LIKE '%鲜%' OR ${tableAlias}.product_name LIKE '%凤肠%')
      AND
      -- 排除副产品
      (${tableAlias}.category IS NULL OR ${tableAlias}.category != '副产品')
    )`;
  }

  /**
   * 生成销售数据的SQL WHERE子句（包含鲜品）
   * 用于销售和生产数据
   * 注意：客户字段筛选已在数据导入阶段处理，此处处理数据库层筛选
   */
  static getSalesFilter(tableAlias: string = 'p'): string {
    return `(
      -- 销售数据包含鲜品，只排除副产品
      (${tableAlias}.category IS NULL OR ${tableAlias}.category != '副产品')
      AND
      -- 确保产品名称不为空（数据质量验证）
      ${tableAlias}.product_name IS NOT NULL 
      AND ${tableAlias}.product_name != ''
    )`;
  }

  /**
   * 生成价格调整数据的SQL WHERE子句
   * 用于价格监控数据
   */
  static getPriceAdjustmentFilter(tableAlias: string = 'pa'): string {
    return `(
      -- 排除鲜品，但保留凤肠产品
      (${tableAlias}.product_name NOT LIKE '%鲜%' OR ${tableAlias}.product_name LIKE '%凤肠%')
      AND
      -- 排除副产品（如果有分类信息）
      (${tableAlias}.category IS NULL OR ${tableAlias}.category != '副产品')
      AND
      -- 价格必须大于0
      ${tableAlias}.current_price > 0
      AND ${tableAlias}.previous_price > 0
    )`;
  }

  /**
   * 生成完整的过滤条件（可配置）
   */
  static getCompleteFilter(
    includeFreshProducts: boolean = false,
    tableAlias: string = 'p'
  ): string {
    let conditions: string[] = [];

    // 鲜品过滤
    if (!includeFreshProducts) {
      conditions.push(
        `(${tableAlias}.product_name NOT LIKE '%鲜%' OR ${tableAlias}.product_name LIKE '%凤肠%')`
      );
    }

    // 副产品过滤
    conditions.push(
      `(${tableAlias}.category IS NULL OR ${tableAlias}.category != '副产品')`
    );

    return conditions.length > 0 ? `(${conditions.join(' AND ')})` : '1=1';
  }

  /**
   * 生成基于选项的动态过滤条件
   */
  static buildDynamicFilter(options: FilterOptions, tableAlias: string = 'p'): string {
    const conditions: string[] = [];

    // 鲜品过滤
    if (!options.includeFreshProducts) {
      conditions.push(
        `(${tableAlias}.product_name NOT LIKE '%鲜%' OR ${tableAlias}.product_name LIKE '%凤肠%')`
      );
    }

    // 副产品过滤
    if (!options.includeByProducts) {
      conditions.push(
        `(${tableAlias}.category IS NULL OR ${tableAlias}.category != '副产品')`
      );
    }

    // 日期范围过滤
    if (options.dateRange) {
      conditions.push(
        `${tableAlias}.record_date BETWEEN '${options.dateRange.start}' AND '${options.dateRange.end}'`
      );
    }

    // 产品ID过滤
    if (options.productIds && options.productIds.length > 0) {
      const ids = options.productIds.join(',');
      conditions.push(`${tableAlias}.product_id IN (${ids})`);
    }

    // 分类过滤
    if (options.categories && options.categories.length > 0) {
      const categories = options.categories.map(c => `'${c}'`).join(',');
      conditions.push(`${tableAlias}.category IN (${categories})`);
    }

    return conditions.length > 0 ? conditions.join(' AND ') : '1=1';
  }

  /**
   * 检查产品名称是否应该被过滤
   */
  static shouldFilterProduct(
    productName: string, 
    category: string | null = null,
    includeFreshProducts: boolean = false
  ): boolean {
    // 检查鲜品
    if (!includeFreshProducts && productName.includes('鲜') && !productName.includes('凤肠')) {
      return true;
    }

    // 检查副产品
    if (category === '副产品') {
      return true;
    }

    return false;
  }

  /**
   * 过滤产品数组
   */
  static filterProducts<T extends { product_name: string; category?: string | null }>(
    products: T[],
    includeFreshProducts: boolean = false
  ): T[] {
    return products.filter(product => 
      !this.shouldFilterProduct(product.product_name, product.category, includeFreshProducts)
    );
  }
}

/**
 * 日期过滤器工具
 */
export class DateFilter {
  /**
   * 获取日期范围的SQL条件
   */
  static getDateRangeCondition(
    startDate: string,
    endDate: string,
    dateField: string = 'record_date'
  ): string {
    return `${dateField} BETWEEN '${startDate}' AND '${endDate}'`;
  }

  /**
   * 获取最近N天的日期范围
   */
  static getRecentDaysRange(days: number): { start: string; end: string } {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - days);

    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    };
  }

  /**
   * 获取本月日期范围
   */
  static getCurrentMonthRange(): { start: string; end: string } {
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    return {
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    };
  }

  /**
   * 验证日期格式
   */
  static isValidDateFormat(dateString: string): boolean {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    if (!regex.test(dateString)) return false;

    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  /**
   * 格式化日期为YYYY-MM-DD格式
   */
  static formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toISOString().split('T')[0];
  }
}

/**
 * 数值过滤器工具
 */
export class NumericFilter {
  /**
   * 生成数值范围条件
   */
  static getRangeCondition(
    field: string,
    min?: number,
    max?: number
  ): string {
    const conditions: string[] = [];

    if (min !== undefined) {
      conditions.push(`${field} >= ${min}`);
    }

    if (max !== undefined) {
      conditions.push(`${field} <= ${max}`);
    }

    return conditions.length > 0 ? conditions.join(' AND ') : '1=1';
  }

  /**
   * 生成TOP N条件
   */
  static getTopNCondition(n: number): string {
    return `LIMIT ${n}`;
  }

  /**
   * 生成百分比变化条件
   */
  static getPercentageChangeCondition(
    currentField: string,
    previousField: string,
    minPercentage: number
  ): string {
    return `ABS((${currentField} - ${previousField}) / NULLIF(${previousField}, 0)) >= ${minPercentage / 100}`;
  }
}
