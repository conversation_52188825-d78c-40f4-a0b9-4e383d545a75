/**
 * 数据库工具函数
 * 提供统一的数据库访问和查询构建功能
 */

import type { D1Database } from '@cloudflare/workers-types';
import type { 
  QueryResult, 
  BatchResult, 
  DatabaseConfig,
  PaginationParams,
  PaginatedResult 
} from '../types/database';

/**
 * 数据库连接管理器
 */
export class DatabaseManager {
  private db: D1Database;
  private config: DatabaseConfig;

  constructor(database: D1Database, config: Partial<DatabaseConfig> = {}) {
    this.db = database;
    this.config = {
      database,
      timeout: config.timeout || 30000,
      retries: config.retries || 3,
      ...config
    };
  }

  /**
   * 执行单个查询
   */
  async query<T = any>(sql: string, params: any[] = []): Promise<QueryResult<T>> {
    try {
      const stmt = this.db.prepare(sql);
      const boundStmt = params.length > 0 ? stmt.bind(...params) : stmt;
      const result = await boundStmt.all();
      
      return {
        results: result.results as T[],
        success: true,
        meta: result.meta
      };
    } catch (error) {
      console.error('Database query failed:', error);
      throw error;
    }
  }

  /**
   * 执行单行查询
   */
  async queryFirst<T = any>(sql: string, params: any[] = []): Promise<T | null> {
    try {
      const stmt = this.db.prepare(sql);
      const boundStmt = params.length > 0 ? stmt.bind(...params) : stmt;
      const result = await boundStmt.first<T>();
      
      return result || null;
    } catch (error) {
      console.error('Database query first failed:', error);
      throw error;
    }
  }

  /**
   * 执行批量操作
   */
  async batch(statements: any[]): Promise<BatchResult> {
    try {
      const startTime = Date.now();
      const results = await this.db.batch(statements);
      const duration = Date.now() - startTime;
      
      return {
        success: true,
        count: results.length,
        duration,
        results
      };
    } catch (error) {
      console.error('Database batch operation failed:', error);
      throw error;
    }
  }

  /**
   * 执行事务
   */
  async transaction(operations: (db: D1Database) => Promise<any[]>): Promise<any[]> {
    try {
      const statements = await operations(this.db);
      const result = await this.db.batch(statements);
      return result;
    } catch (error) {
      console.error('Database transaction failed:', error);
      throw error;
    }
  }

  /**
   * 分页查询
   */
  async queryPaginated<T = any>(
    sql: string, 
    params: any[] = [], 
    pagination: PaginationParams
  ): Promise<PaginatedResult<T>> {
    try {
      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM (${sql})`;
      const countResult = await this.queryFirst<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 计算分页参数
      const totalPages = Math.ceil(total / pagination.limit);
      const offset = (pagination.page - 1) * pagination.limit;

      // 执行分页查询
      const paginatedSql = `${sql} LIMIT ${pagination.limit} OFFSET ${offset}`;
      const dataResult = await this.query<T>(paginatedSql, params);

      return {
        data: dataResult.results,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages,
          hasNext: pagination.page < totalPages,
          hasPrev: pagination.page > 1
        }
      };
    } catch (error) {
      console.error('Paginated query failed:', error);
      throw error;
    }
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): D1Database {
    return this.db;
  }
}

/**
 * SQL查询构建器
 */
export class QueryBuilder {
  private selectFields: string[] = [];
  private fromTable: string = '';
  private whereConditions: string[] = [];
  private joinClauses: string[] = [];
  private orderByClause: string = '';
  private limitClause: string = '';
  private groupByClause: string = '';

  select(fields: string[]): QueryBuilder {
    this.selectFields = fields;
    return this;
  }

  from(table: string): QueryBuilder {
    this.fromTable = table;
    return this;
  }

  where(condition: string): QueryBuilder {
    this.whereConditions.push(condition);
    return this;
  }

  join(table: string, condition: string): QueryBuilder {
    this.joinClauses.push(`JOIN ${table} ON ${condition}`);
    return this;
  }

  leftJoin(table: string, condition: string): QueryBuilder {
    this.joinClauses.push(`LEFT JOIN ${table} ON ${condition}`);
    return this;
  }

  orderBy(field: string, direction: 'ASC' | 'DESC' = 'ASC'): QueryBuilder {
    this.orderByClause = `ORDER BY ${field} ${direction}`;
    return this;
  }

  groupBy(fields: string[]): QueryBuilder {
    this.groupByClause = `GROUP BY ${fields.join(', ')}`;
    return this;
  }

  limit(count: number): QueryBuilder {
    this.limitClause = `LIMIT ${count}`;
    return this;
  }

  build(): string {
    let sql = `SELECT ${this.selectFields.join(', ')} FROM ${this.fromTable}`;
    
    if (this.joinClauses.length > 0) {
      sql += ` ${this.joinClauses.join(' ')}`;
    }
    
    if (this.whereConditions.length > 0) {
      sql += ` WHERE ${this.whereConditions.join(' AND ')}`;
    }
    
    if (this.groupByClause) {
      sql += ` ${this.groupByClause}`;
    }
    
    if (this.orderByClause) {
      sql += ` ${this.orderByClause}`;
    }
    
    if (this.limitClause) {
      sql += ` ${this.limitClause}`;
    }
    
    return sql;
  }
}

/**
 * 数据库工具函数
 */
export const DatabaseUtils = {
  /**
   * 转义SQL字符串
   */
  escapeString(str: string): string {
    return str.replace(/'/g, "''");
  },

  /**
   * 格式化日期为SQL格式
   */
  formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toISOString().split('T')[0];
  },

  /**
   * 验证日期格式
   */
  isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && dateString.match(/^\d{4}-\d{2}-\d{2}$/);
  },

  /**
   * 生成占位符字符串
   */
  generatePlaceholders(count: number): string {
    return Array(count).fill('?').join(', ');
  },

  /**
   * 构建IN子句
   */
  buildInClause(values: any[]): { clause: string; params: any[] } {
    const placeholders = this.generatePlaceholders(values.length);
    return {
      clause: `IN (${placeholders})`,
      params: values
    };
  }
};
